﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Metabolomics.Core;
using Metabolomics.MsLima.Reader;
using Metabolomics.MsLima.Model;

namespace Metabolomics.MsLima.Bean
{
    public class DataStorageBean
    {
        #region Properties
        public List<MassSpectrum> RawLibraryFile { get; set; }
        public List<CompoundBean> CompoundList { get; set; }

        public string FilePath { get; set; }
        public string OriginalFilePath { get; set; }
        public LibraryFileFormat FileFormat { get; set; }


        #endregion

        public DataStorageBean() { }

        public void SetLibrary(string filePath, CompoundGroupingKey key)
        {
            FilePath = filePath;
            ReadLibraryFile();
            CompoundList = CompoundGroupUtility.CreateCompoundList(RawLibraryFile, key);
        }

        public async Task SetLibraryAsync(string filePath, CompoundGroupingKey key, IProgress<int> progress = null)
        {
            FilePath = filePath;
            await ReadLibraryFileAsync(progress);

            // Use async compound list creation for better performance
            CompoundList = await CompoundGroupUtility.CreateCompoundListAsync(RawLibraryFile, key, progress);
        }

        public async Task SetLibraryAsync(string filePath, CompoundGroupingKey key, IProgress<int> progress, CancellationToken cancellationToken)
        {
            FilePath = filePath;
            await ReadLibraryFileAsync(progress, cancellationToken);

            // Use async compound list creation for better performance with cancellation support
            CompoundList = await CompoundGroupUtility.CreateCompoundListAsync(RawLibraryFile, key, progress);
        }

        public void SetMassBankLibrary(string filePath, CompoundGroupingKey key)
        {
            FilePath = filePath;
            this.FileFormat = LibraryFileFormat.MassBank;
            this.RawLibraryFile = ReadMassBankFile.ReadAsMsSpectra(FilePath);
            CompoundList = CompoundGroupUtility.CreateCompoundList(RawLibraryFile, key);
        }

        public async Task SetMassBankLibraryAsync(string filePath, CompoundGroupingKey key, IProgress<int> progress = null)
        {
            FilePath = filePath;
            this.FileFormat = LibraryFileFormat.MassBank;
            this.RawLibraryFile = ReadMassBankFile.ReadAsMsSpectra(FilePath);
            CompoundList = CompoundGroupUtility.CreateCompoundList(RawLibraryFile, key);
        }



        public void ReadLibraryFile()
        {
            var extention = Path.GetExtension(FilePath).ToLower();
            if (extention == ".mgf")
            {
                this.FileFormat = LibraryFileFormat.Mgf;
                this.RawLibraryFile = ReadMgfFile.ReadAsMsSpectra(FilePath);
            }
            else if (extention == ".msp")
            {
                this.FileFormat = LibraryFileFormat.Msp;
                this.RawLibraryFile = ReadMspFile.ReadAsMsSpectra(FilePath);
            }
            else if (extention == ".txt")
            {
                this.FileFormat = LibraryFileFormat.Text;
                this.RawLibraryFile = ReadMassBankFile.ReadAsMsSpectra(FilePath);
            }
            else
            {
                this.FileFormat = LibraryFileFormat.Text;
                this.RawLibraryFile = ReadMassBankFile.ReadAsMsSpectra(FilePath);
            }
        }

        public async Task ReadLibraryFileAsync(IProgress<int> progress = null)
        {
            var extention = Path.GetExtension(FilePath).ToLower();
            if (extention == ".mgf")
            {
                this.FileFormat = LibraryFileFormat.Mgf;
                this.RawLibraryFile = ReadMgfFile.ReadAsMsSpectra(FilePath);
            }
            else if (extention == ".msp")
            {
                this.FileFormat = LibraryFileFormat.Msp;
                this.RawLibraryFile = await ReadMspFile.ReadAsMsSpectraAsync(FilePath, progress);
            }
            else if (extention == ".txt")
            {
                this.FileFormat = LibraryFileFormat.Text;
                this.RawLibraryFile = ReadMassBankFile.ReadAsMsSpectra(FilePath);
            }
            else
            {
                this.FileFormat = LibraryFileFormat.Text;
                this.RawLibraryFile = ReadMassBankFile.ReadAsMsSpectra(FilePath);
            }
        }

        public async Task ReadLibraryFileAsync(IProgress<int> progress, CancellationToken cancellationToken)
        {
            var extention = Path.GetExtension(FilePath).ToLower();
            if (extention == ".mgf")
            {
                this.FileFormat = LibraryFileFormat.Mgf;
                this.RawLibraryFile = ReadMgfFile.ReadAsMsSpectra(FilePath);
            }
            else if (extention == ".msp")
            {
                this.FileFormat = LibraryFileFormat.Msp;
                this.RawLibraryFile = await ReadMspFile.ReadAsMsSpectraAsync(FilePath, progress, cancellationToken);
            }
            else if (extention == ".txt")
            {
                this.FileFormat = LibraryFileFormat.Text;
                this.RawLibraryFile = ReadMassBankFile.ReadAsMsSpectra(FilePath);
            }
            else
            {
                this.FileFormat = LibraryFileFormat.Text;
                this.RawLibraryFile = ReadMassBankFile.ReadAsMsSpectra(FilePath);
            }
        }
    }
}
