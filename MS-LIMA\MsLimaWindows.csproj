﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D5CEF2D5-3B54-4EEB-BFE3-523F83FD32A6}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Metabolomics.MsLima</RootNamespace>
    <AssemblyName>MS-LIMA</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>Metabolomics.MsLima.App</StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>MSP-Viewer2.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="IKVM.OpenJDK.XML.Parse, Version=7.4.5196.0, Culture=neutral, PublicKeyToken=13235d27fcbfff58, processorArchitecture=MSIL">
      <HintPath>..\packages\IKVM.7.4.5196.0\lib\IKVM.OpenJDK.XML.Parse.dll</HintPath>
    </Reference>
    <Reference Include="MessagePack, Version=1.7.3.4, Culture=neutral, PublicKeyToken=b4a0369545f0a1be, processorArchitecture=MSIL">
      <HintPath>..\packages\MessagePack.1.7.3.4\lib\net47\MessagePack.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Expression.Interactions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Windows.Interactivity.WPF.2.0.20525\lib\net40\Microsoft.Expression.Interactions.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\PresentationFramework.4.6.0\lib\PresentationFramework.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.4.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.5.2\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Common, Version=4.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Drawing.Common.4.5.1\lib\net461\System.Drawing.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.2\lib\netstandard2.0\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Interactivity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Windows.Interactivity.WPF.2.0.20525\lib\net40\System.Windows.Interactivity.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="Model\ComparativeSpectrumViewerModel.cs" />
    <Compile Include="ViewModel\ComparativeSpectrumViewerVM.cs" />
    <Compile Include="Model\WindowUtility.cs" />
    <Compile Include="ViewModel\AllSpectraMetaInformationTableVM.cs" />
    <Compile Include="ViewModel\ParameterSettingVM.cs" />
    <Compile Include="ViewModel\SaveChartDrawingVM.cs" />
    <Compile Include="ViewModel\ShortMessageVM.cs" />
    <Compile Include="Services\ThemeManager.cs" />
    <Compile Include="Services\ThemeService.cs" />
    <Compile Include="Utility\DynamicColumnManager.cs" />
    <Compile Include="Controls\ThemeToggle.xaml.cs">
      <DependentUpon>ThemeToggle.xaml</DependentUpon>
    </Compile>
    <Compile Include="Controls\ColorSchemeSelector.xaml.cs">
      <DependentUpon>ColorSchemeSelector.xaml</DependentUpon>
    </Compile>
    <Compile Include="Window\AllSpectraMetaInformationTable.xaml.cs">
      <DependentUpon>AllSpectraMetaInformationTable.xaml</DependentUpon>
    </Compile>
    <Compile Include="Window\ComparativeSpectrumViewer.xaml.cs">
      <DependentUpon>ComparativeSpectrumViewer.xaml</DependentUpon>
    </Compile>
    <Compile Include="Window\ParameterSetting.xaml.cs">
      <DependentUpon>ParameterSetting.xaml</DependentUpon>
    </Compile>
    <Compile Include="Window\SaveChartDrawing.xaml.cs">
      <DependentUpon>SaveChartDrawing.xaml</DependentUpon>
    </Compile>
    <Compile Include="Window\ShortMessage.xaml.cs">
      <DependentUpon>ShortMessage.xaml</DependentUpon>
    </Compile>


    <Page Include="Controls\ThemeToggle.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Controls\ColorSchemeSelector.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="MainWindow\MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Themes\LightTheme.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Themes\DarkTheme.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Themes\ModernStyles.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MainWindow\MainWindowModel.cs" />
    <Compile Include="Model\ControlRefresh.cs" />
    <Compile Include="Model\ExportUtility.cs" />
    <Compile Include="Model\ImportUtility.cs" />
    <Compile Include="Model\Converters.cs" />
    <Compile Include="WindowUtility\FilteredTableCollection.cs" />
    <Compile Include="MainWindow\MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Page Include="Window\AllSpectraMetaInformationTable.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Window\ComparativeSpectrumViewer.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Window\ParameterSetting.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Window\SaveChartDrawing.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Window\ShortMessage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>

  </ItemGroup>
  <ItemGroup>
    <Compile Include="MainWindow\MainWindowVM.cs" />
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\ChartDrawing\ChartDrawing.csproj">
      <Project>{6131640b-30e6-4606-a394-be953590f3e9}</Project>
      <Name>ChartDrawing</Name>
    </ProjectReference>
    <ProjectReference Include="..\CommonWindows\CommonMVVM.csproj">
      <Project>{691f7770-0efb-4761-97db-205f56c3e848}</Project>
      <Name>CommonMVVM</Name>
    </ProjectReference>
    <ProjectReference Include="..\Common\Common.csproj">
      <Project>{6d8d81ca-8d7c-4799-a58b-58fc0bec651c}</Project>
      <Name>Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\MS-LIMA-CommonView\MsLimaCommonView.csproj">
      <Project>{e4626675-9f5a-4ebe-9433-6e46413262ab}</Project>
      <Name>MsLimaCommonView</Name>
    </ProjectReference>
    <ProjectReference Include="..\MS-LIMA-Core\MsLimaBase.csproj">
      <Project>{7C4124E9-0A9D-4FFB-BB5D-4A7DD9CFD21C}</Project>
      <Name>MsLimaBase</Name>
    </ProjectReference>
    <ProjectReference Include="..\ViewFromSmiles\ViewFromSmiles.csproj">
      <Project>{66163ad9-3884-4150-adac-0b2402b1a001}</Project>
      <Name>ViewFromSmiles</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resources\MSP-Viewer2.ico" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="MSP-Viewer2.ico" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="健康风险数据库logo 4.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>