using java.awt;
using java.awt.image;
using java.io;
using java.lang;
using java.util;
using javax.imageio;
using org.openscience.cdk.interfaces;
using org.openscience.cdk.layout;
using org.openscience.cdk.renderer;
using org.openscience.cdk.renderer.font;
using org.openscience.cdk.renderer.generators;
using org.openscience.cdk.renderer.visitor;
using org.openscience.cdk.silent;
using org.openscience.cdk.graph;
using org.openscience.cdk.tools;
using org.openscience.cdk.smiles;
using org.openscience.cdk.exception;
using org.openscience.cdk.tools.manipulator;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;

namespace Metabolomics.Core
{
    public class SmilesConverter
    {
        // Simple theme detection delegate - can be set by the main application
        public static Func<bool> IsDarkThemeFunc { get; set; }

        public static void TryClassLoad()
        {
            try
            {
                SmilesToImage("C", 300, 300);
            }
            catch (ClassNotFoundException ex)
            {
                ex.printStackTrace();
            }
        }

        public static System.Drawing.Image SmilesToImage(string smiles, int width, int height)
        {
            try {
                if (width <= 0 || height <= 0) return null;
                if (string.IsNullOrEmpty(smiles)) return null;

                var errorString = string.Empty;
                var iAtomContainer = SmilesToIAtomContainer(smiles, out errorString);
                if (iAtomContainer == null) return null;

                // THEME-SPECIFIC RENDERING: Use different methods based on current theme
                bool isDarkTheme = IsDarkThemeFunc?.Invoke() ?? false;

                if (isDarkTheme)
                {
                    // Dark theme: Use current optimized rendering method
                    return IAtomContainerToImage(iAtomContainer, width, height);
                }
                else
                {
                    // Light theme: Use legacy rendering method from SmilesConverter_old.cs
                    return IAtomContainerToImageLegacy(iAtomContainer, width, height);
                }
            } catch (java.lang.Exception ex) {
                // Log the error and return null to prevent crashes
                java.lang.System.err.println("Error in SmilesToImage: " + ex.getMessage());
                return null;
            } catch (System.Exception ex) {
                // Handle .NET exceptions as well
                System.Diagnostics.Debug.WriteLine($"Error in SmilesToImage: {ex.Message}");
                return null;
            }
        }


        public static IAtomContainer SmilesToIAtomContainer(string smiles, out string error)
        {
            error = "Error\r\n";

            IAtomContainer container = null;
            try
            {
                var smilesParser = new SmilesParser(SilentChemObjectBuilder.getInstance());
                container = smilesParser.parseSmiles(smiles);
            }
            catch (InvalidSmilesException)
            {
                error += "SMILES: cannot be converted.\r\n";
                return null;
            }

            if (!ConnectivityChecker.isConnected(container))
            {
                error += "SMILES: the connectivity is not correct.\r\n";
                return null;
            }

            return container;
        }

        public static System.Drawing.Image IAtomContainerToImage(IAtomContainer iAtomContainer, int width, int height, bool isNumbered = false)
        {
            var errorString = string.Empty;

            if (width <= 0 || height <= 0) return null;
            if (iAtomContainer == null) return null;

            IMolecule molecule = new Molecule(iAtomContainer);
            //AtomContainerManipulator.convertImplicitToExplicitHydrogens(molecule);

            var drawArea = new Rectangle(width, height);
            var image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

            var sdg = new StructureDiagramGenerator();
            sdg.setMolecule(molecule);
            sdg.generateCoordinates();
            molecule = sdg.getMolecule();

            var generators = new ArrayList();
            // CRITICAL: Keep BasicSceneGenerator but configure it properly to prevent crashes
            generators.add(new BasicSceneGenerator());
            generators.add(new BasicBondGenerator());
            generators.add(new BasicAtomGenerator());
            if (isNumbered) generators.add(new AtomNumberGenerator());

            var renderer = new AtomContainerRenderer(generators, new AWTFontManager());
            renderer.setup(molecule, drawArea);

            var renderModel = renderer.getRenderer2DModel();

            // Configure theme-aware colors
            bool isDarkTheme = IsDarkThemeFunc?.Invoke() ?? false;

            // Configure scene generator to not draw backgrounds
            try {
                renderModel.set(typeof(BasicSceneGenerator.BackgroundColor), new Color(0, 0, 0, 0)); // Transparent
            } catch (java.lang.Exception) {
                // Property may not exist in this CDK version
            }

            if (isDarkTheme)
            {
                // Dark theme: EMERGENCY ROLLBACK - Use proven working configuration
                renderModel.set(typeof(BasicBondGenerator.DefaultBondColor), Color.WHITE);

                // Enable element-specific colors for heteroatoms (N=blue, O=red, S=yellow, P=orange)
                renderModel.set(typeof(BasicAtomGenerator.ColorByType), java.lang.Boolean.TRUE);

                // Configure atom display settings for better visibility
                renderModel.set(typeof(BasicAtomGenerator.ShowEndCarbons), java.lang.Boolean.FALSE);
                renderModel.set(typeof(BasicAtomGenerator.KekuleStructure), java.lang.Boolean.FALSE);
                renderModel.set(typeof(BasicAtomGenerator.CompactShape), java.lang.Boolean.TRUE);

                // Use conservative radius that was working
                renderModel.set(typeof(BasicAtomGenerator.AtomRadius), 3.0);

                // Set atom text color to white for dark theme
                renderModel.set(typeof(BasicAtomGenerator.AtomColor), Color.WHITE);

                // EMERGENCY ROLLBACK: Focus on basic functionality first
                // Bond visibility improvements will be added incrementally after basic rendering is confirmed

                // Use minimal rendering settings to reduce background artifacts while preserving element colors
            }
            else
            {
                // Light theme: EMERGENCY ROLLBACK - Use proven working configuration
                renderModel.set(typeof(BasicBondGenerator.DefaultBondColor), Color.BLACK);

                // Enable element-specific colors for heteroatoms (N=blue, O=red, S=yellow, P=orange)
                renderModel.set(typeof(BasicAtomGenerator.ColorByType), java.lang.Boolean.TRUE);

                renderModel.set(typeof(BasicAtomGenerator.ShowEndCarbons), java.lang.Boolean.FALSE);
                renderModel.set(typeof(BasicAtomGenerator.KekuleStructure), java.lang.Boolean.FALSE);
                renderModel.set(typeof(BasicAtomGenerator.CompactShape), java.lang.Boolean.TRUE);

                // Use conservative radius that was working
                renderModel.set(typeof(BasicAtomGenerator.AtomRadius), 3.0);

                // Set atom text color to black for light theme
                renderModel.set(typeof(BasicAtomGenerator.AtomColor), Color.BLACK);

                // EMERGENCY ROLLBACK: Focus on basic functionality first
                // Bond visibility improvements will be added incrementally after basic rendering is confirmed
            }

            if (isNumbered)
            {
                renderModel.set(typeof(AtomNumberGenerator.Offset), new javax.vecmath.Vector2d(0, 15));
                renderModel.set(typeof(AtomNumberGenerator.WillDrawAtomNumbers), java.lang.Boolean.TRUE);
                renderModel.set(typeof(AtomNumberGenerator.ColorByType), java.lang.Boolean.TRUE);

                // Apply theme colors to atom numbers as well
                if (isDarkTheme)
                {
                    renderModel.set(typeof(AtomNumberGenerator.AtomNumberTextColor), Color.WHITE);
                }
                else
                {
                    renderModel.set(typeof(AtomNumberGenerator.AtomNumberTextColor), Color.BLACK);
                }
            }

            var g2 = (Graphics2D)image.getGraphics();
            g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            // Use theme-aware background color (reuse the same isDarkTheme variable)
            Color backgroundColor;
            if (isDarkTheme)
            {
                // Dark theme background
                backgroundColor = new Color(30, 30, 30);
                g2.setColor(backgroundColor);
            }
            else
            {
                // Light theme background
                backgroundColor = Color.WHITE;
                g2.setColor(backgroundColor);
            }
            g2.fillRect(0, 0, width, height);

            // CRITICAL: Remove Graphics2D color overrides that interfere with CDK rendering
            // Let CDK handle all color and stroke settings through the render model
            // Only set basic rendering quality hints
            try {
                g2.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1.0f));
            } catch (java.lang.Exception) {
                // Fallback if composite mode fails
            }

            // Calculate diagram bounds for the molecule
            var diagramBounds = renderer.calculateDiagramBounds((IAtomContainer)molecule);

            // Calculate scale factor to ensure molecules fit well with padding
            // Use 0.9 scale factor to provide better visibility while ensuring complete fit
            double scaleX = (width * 0.9) / diagramBounds.getWidth();
            double scaleY = (height * 0.9) / diagramBounds.getHeight();
            double scale = System.Math.Min(scaleX, scaleY);

            // Calculate the center of the canvas
            double canvasCenterX = width / 2.0;
            double canvasCenterY = height / 2.0;

            // Calculate the center of the original diagram bounds
            double diagramCenterX = diagramBounds.getCenterX();
            double diagramCenterY = diagramBounds.getCenterY();

            // Apply transformations step by step for better control
            // 1. Translate to canvas center
            g2.translate(canvasCenterX, canvasCenterY);
            // 2. Apply scaling
            g2.scale(scale, scale);
            // 3. Translate to center the diagram at origin
            g2.translate(-diagramCenterX, -diagramCenterY);

            // HETEROATOM BACKGROUND FIX: Draw opaque backgrounds behind heteroatom labels BEFORE CDK rendering
            try {
                DrawHeteroatomBackgrounds(g2, molecule, scale, canvasCenterX, canvasCenterY, diagramCenterX, diagramCenterY, isDarkTheme);
            } catch (java.lang.Exception e) {
                java.lang.System.err.println("Heteroatom background rendering failed: " + e.getMessage());
                // Continue without backgrounds - basic rendering still works
            }

            renderer.paint(molecule, new AWTDrawVisitor(g2));

            // HALOGEN COLOR ENHANCEMENT: Post-processing approach for Cl and F atoms (FIXED - precise targeting)
            try {
                ApplyHalogenColors((BufferedImage)image, molecule, scale, canvasCenterX, canvasCenterY, diagramCenterX, diagramCenterY, isDarkTheme);
            } catch (java.lang.Exception e) {
                java.lang.System.err.println("Halogen color enhancement failed: " + e.getMessage());
                // Continue without halogen colors - basic rendering still works
            }

            // SAFE: Conservative post-processing for dark theme heteroatom backgrounds only
            if (isDarkTheme)
            {
                try {
                    // Simple, safe post-processing - only target obvious heteroatom backgrounds
                    var imageData = (BufferedImage)image;
                    var imgWidth = imageData.getWidth();
                    var imgHeight = imageData.getHeight();

                    if (imgWidth > 0 && imgHeight > 0) {
                        // Very conservative approach - only replace pure white pixels that are isolated
                        for (int x = 1; x < imgWidth - 1; x++) {
                            for (int y = 1; y < imgHeight - 1; y++) {
                                int rgb = imageData.getRGB(x, y);
                                int red = (rgb >> 16) & 0xFF;
                                int green = (rgb >> 8) & 0xFF;
                                int blue = rgb & 0xFF;

                                // Only replace pure white pixels (255,255,255) that are likely backgrounds
                                if (red == 255 && green == 255 && blue == 255) {
                                    // EMERGENCY ROLLBACK: Ultra-conservative post-processing
                                    // Check if this is an isolated white pixel (likely heteroatom background)
                                    // Count white neighbors - if too many, it's probably a bond
                                    int whiteNeighbors = 0;

                                    for (int dx = -1; dx <= 1; dx++) {
                                        for (int dy = -1; dy <= 1; dy++) {
                                            if (dx == 0 && dy == 0) continue;
                                            int nx = x + dx;
                                            int ny = y + dy;
                                            int nrgb = imageData.getRGB(nx, ny);
                                            int nred = (nrgb >> 16) & 0xFF;
                                            int ngreen = (nrgb >> 8) & 0xFF;
                                            int nblue = nrgb & 0xFF;
                                            if (nred >= 240 && ngreen >= 240 && nblue >= 240) {
                                                whiteNeighbors++;
                                            }
                                        }
                                    }

                                    // EMERGENCY: Only remove completely isolated white pixels (0 neighbors)
                                    if (whiteNeighbors == 0) {
                                        int alpha = (rgb >> 24) & 0xFF;
                                        int newRgb = (alpha << 24) | (30 << 16) | (30 << 8) | 30;
                                        imageData.setRGB(x, y, newRgb);
                                    }
                                }
                            }
                        }
                    }
                } catch (java.lang.Exception e) {
                    java.lang.System.err.println("Conservative post-processing failed: " + e.getMessage());
                    // Continue without post-processing - basic rendering should still work
                }
            }

            var baos = new ByteArrayOutputStream();

            try
            {
                ImageIO.write((RenderedImage)image, "PNG", baos);
            }
            catch (java.io.IOException e)
            {
                e.printStackTrace();
            }
            finally
            {
            }

            if (baos == null) return null;

            var sendData = baos.toByteArray();
            return ByteArrayToImage(sendData);
        }

        /// <summary>
        /// LEGACY RENDERING METHOD: Optimized for light theme with white background
        /// Based on the enhanced method from SmilesConverter_old.cs
        /// </summary>
        public static System.Drawing.Image IAtomContainerToImageLegacy(IAtomContainer iAtomContainer, int width, int height, bool isNumbered = false)
        {
            try {
                if (width <= 0 || height <= 0) return null;
                if (iAtomContainer == null) return null;

                // Create molecule and generate coordinates using legacy approach
                var molecule = new org.openscience.cdk.silent.Molecule(iAtomContainer);
                var drawArea = new java.awt.Rectangle(width, height);
                var image = new java.awt.image.BufferedImage(width, height, java.awt.image.BufferedImage.TYPE_INT_RGB);

                var sdg = new org.openscience.cdk.layout.StructureDiagramGenerator();
                sdg.setMolecule(molecule);
                sdg.generateCoordinates();
                molecule = (org.openscience.cdk.silent.Molecule)sdg.getMolecule();

                // Use same generator setup as legacy method
                var generators = new java.util.ArrayList();
                generators.add(new org.openscience.cdk.renderer.generators.BasicSceneGenerator());
                generators.add(new org.openscience.cdk.renderer.generators.BasicBondGenerator());
                generators.add(new org.openscience.cdk.renderer.generators.BasicAtomGenerator());
                if (isNumbered) generators.add(new org.openscience.cdk.renderer.generators.AtomNumberGenerator());

                var renderer = new org.openscience.cdk.renderer.AtomContainerRenderer(generators, new org.openscience.cdk.renderer.font.AWTFontManager());
                renderer.setup(molecule, drawArea);

                var renderModel = renderer.getRenderer2DModel();

                // LIGHT THEME CONFIGURATION: Optimized for white background
                renderModel.set(typeof(org.openscience.cdk.renderer.generators.BasicBondGenerator.DefaultBondColor), java.awt.Color.BLACK);
                renderModel.set(typeof(org.openscience.cdk.renderer.generators.BasicAtomGenerator.AtomColor), java.awt.Color.BLACK);

                // Enable element-specific colors for heteroatoms
                renderModel.set(typeof(org.openscience.cdk.renderer.generators.BasicAtomGenerator.ColorByType), java.lang.Boolean.TRUE);
                renderModel.set(typeof(org.openscience.cdk.renderer.generators.BasicAtomGenerator.ShowEndCarbons), java.lang.Boolean.FALSE);
                renderModel.set(typeof(org.openscience.cdk.renderer.generators.BasicAtomGenerator.KekuleStructure), java.lang.Boolean.FALSE);
                renderModel.set(typeof(org.openscience.cdk.renderer.generators.BasicAtomGenerator.CompactShape), java.lang.Boolean.TRUE);
                renderModel.set(typeof(org.openscience.cdk.renderer.generators.BasicAtomGenerator.AtomRadius), 3.0);

                // Configure numbering if requested
                if (isNumbered) {
                    renderModel.set(typeof(org.openscience.cdk.renderer.generators.AtomNumberGenerator.Offset), new javax.vecmath.Vector2d(0, 15));
                    renderModel.set(typeof(org.openscience.cdk.renderer.generators.AtomNumberGenerator.WillDrawAtomNumbers), java.lang.Boolean.TRUE);
                    renderModel.set(typeof(org.openscience.cdk.renderer.generators.AtomNumberGenerator.ColorByType), java.lang.Boolean.TRUE);
                    renderModel.set(typeof(org.openscience.cdk.renderer.generators.AtomNumberGenerator.AtomNumberTextColor), java.awt.Color.BLACK);
                }

                var g2 = (java.awt.Graphics2D)image.getGraphics();
                g2.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING, java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
                g2.setRenderingHint(java.awt.RenderingHints.KEY_RENDERING, java.awt.RenderingHints.VALUE_RENDER_QUALITY);
                g2.setRenderingHint(java.awt.RenderingHints.KEY_TEXT_ANTIALIASING, java.awt.RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

                // Light theme: White background
                g2.setColor(java.awt.Color.WHITE);
                g2.fillRect(0, 0, width, height);

                // Calculate diagram bounds and transformations (same as legacy method)
                var diagramBounds = renderer.calculateDiagramBounds((org.openscience.cdk.interfaces.IAtomContainer)molecule);

                double scaleX = (width * 0.9) / diagramBounds.getWidth();
                double scaleY = (height * 0.9) / diagramBounds.getHeight();
                double scale = System.Math.Min(scaleX, scaleY);

                double canvasCenterX = width / 2.0;
                double canvasCenterY = height / 2.0;
                double diagramCenterX = diagramBounds.getCenterX();
                double diagramCenterY = diagramBounds.getCenterY();

                // Apply transformations
                g2.translate(canvasCenterX, canvasCenterY);
                g2.scale(scale, scale);
                g2.translate(-diagramCenterX, -diagramCenterY);

                // Render the molecule
                renderer.paint(molecule, new org.openscience.cdk.renderer.visitor.AWTDrawVisitor(g2));

                // Apply enhanced halogen coloring for light theme
                ApplyHalogenColorsLegacy((java.awt.image.BufferedImage)image, molecule, scale, canvasCenterX, canvasCenterY, diagramCenterX, diagramCenterY, false);

                // Convert to .NET Image
                var baos = new java.io.ByteArrayOutputStream();
                javax.imageio.ImageIO.write((java.awt.image.RenderedImage)image, "PNG", baos);
                var sendData = baos.toByteArray();
                return ByteArrayToImage(sendData);
            } catch (java.lang.Exception ex) {
                java.lang.System.err.println("Error in IAtomContainerToImageLegacy: " + ex.getMessage());
                return null;
            } catch (System.Exception ex) {
                System.Diagnostics.Debug.WriteLine($"Error in IAtomContainerToImageLegacy: {ex.Message}");
                return null;
            }
        }

        public static System.Drawing.Image ByteArrayToImage(byte[] byteArrayIn)
        {
            var ms = new MemoryStream(byteArrayIn);
            var returnImage = System.Drawing.Image.FromStream(ms);
            return returnImage;
        }

        /// <summary>
        /// HETEROATOM BACKGROUND FIX: Draw opaque backgrounds behind heteroatom labels
        /// This prevents bonds from showing through atom text
        /// </summary>
        private static void DrawHeteroatomBackgrounds(Graphics2D g2, IMolecule molecule, double scale,
            double canvasCenterX, double canvasCenterY, double diagramCenterX, double diagramCenterY, bool isDarkTheme)
        {
            if (g2 == null || molecule == null) return;

            try {
                // Define background color based on theme
                Color backgroundColor;
                if (isDarkTheme) {
                    backgroundColor = new Color(30, 30, 30); // Dark background
                } else {
                    backgroundColor = Color.WHITE; // Light background
                }

                java.lang.System.err.println("DEBUG: Drawing heteroatom backgrounds...");

                // Iterate through all atoms to find heteroatoms
                for (int i = 0; i < molecule.getAtomCount(); i++) {
                    var atom = molecule.getAtom(i);
                    if (atom == null) continue;

                    string symbol = atom.getSymbol();

                    // Check if atom is a heteroatom (not carbon or hydrogen)
                    if (symbol != null && !symbol.Equals("C") && !symbol.Equals("H")) {
                        // Get atom coordinates
                        var point2d = atom.getPoint2d();
                        if (point2d == null) continue;

                        // Transform coordinates to image space (same as CDK rendering)
                        double atomX = point2d.x;
                        double atomY = point2d.y;

                        java.lang.System.err.println("DEBUG: Drawing background for heteroatom: " + symbol + " at (" + atomX + ", " + atomY + ")");

                        // Draw background rectangle behind atom label
                        // Note: Graphics2D transformations are already applied, so use original coordinates
                        g2.setColor(backgroundColor);

                        // Calculate background rectangle size based on atom symbol length
                        int bgWidth = symbol.Length * 8 + 4; // Approximate width
                        int bgHeight = 12; // Approximate height

                        // Draw filled rectangle centered on atom position
                        g2.fillRect((int)(atomX - bgWidth/2), (int)(atomY - bgHeight/2), bgWidth, bgHeight);
                    }
                }
            } catch (java.lang.Exception e) {
                java.lang.System.err.println("Error in DrawHeteroatomBackgrounds: " + e.getMessage());
                // Fail silently to preserve basic functionality
            }
        }

        /// <summary>
        /// HALOGEN COLOR ENHANCEMENT: Apply green colors to Cl and F atoms via post-processing
        /// FIXED VERSION: Precise targeting to avoid affecting bonds and other elements
        /// </summary>
        private static void ApplyHalogenColors(BufferedImage image, IMolecule molecule, double scale,
            double canvasCenterX, double canvasCenterY, double diagramCenterX, double diagramCenterY, bool isDarkTheme)
        {
            if (image == null || molecule == null) return;

            try {
                // Define green color for halogens (brighter green for better visibility)
                Color halogenGreen = new Color(0, 180, 0); // Brighter green
                int greenRgb = halogenGreen.getRGB();

                java.lang.System.err.println("DEBUG: Starting halogen color enhancement...");
                java.lang.System.err.println("DEBUG: Molecule has " + molecule.getAtomCount() + " atoms");

                // Strategy 1: Coordinate-based approach (improved)
                ApplyHalogenColorsByCoordinates(image, molecule, scale, canvasCenterX, canvasCenterY, diagramCenterX, diagramCenterY, greenRgb, isDarkTheme);

                // Strategy 2: Comprehensive pixel scanning approach
                ApplyHalogenColorsByPixelScanning(image, molecule, greenRgb, isDarkTheme);

            } catch (java.lang.Exception e) {
                java.lang.System.err.println("Error in ApplyHalogenColors: " + e.getMessage());
                e.printStackTrace();
                // Fail silently to preserve basic functionality
            }
        }

        /// <summary>
        /// Strategy 1: Improved coordinate-based halogen coloring
        /// </summary>
        private static void ApplyHalogenColorsByCoordinates(BufferedImage image, IMolecule molecule, double scale,
            double canvasCenterX, double canvasCenterY, double diagramCenterX, double diagramCenterY, int greenRgb, bool isDarkTheme)
        {
            try {
                // Iterate through all atoms in the molecule
                for (int i = 0; i < molecule.getAtomCount(); i++) {
                    var atom = molecule.getAtom(i);
                    if (atom == null) continue;

                    string symbol = atom.getSymbol();

                    // Check if atom is a halogen (Cl or F)
                    if (symbol != null && (symbol.Equals("Cl") || symbol.Equals("F"))) {
                        java.lang.System.err.println("DEBUG: Found halogen atom: " + symbol);

                        // Get atom coordinates
                        var point2d = atom.getPoint2d();
                        if (point2d == null) {
                            java.lang.System.err.println("DEBUG: No coordinates for atom " + symbol);
                            continue;
                        }

                        // Transform coordinates to image space (same as CDK rendering)
                        double atomX = point2d.x;
                        double atomY = point2d.y;

                        java.lang.System.err.println("DEBUG: Original coordinates: (" + atomX + ", " + atomY + ")");

                        // Apply the same transformations as the main rendering
                        // 1. Translate to center the diagram at origin
                        atomX -= diagramCenterX;
                        atomY -= diagramCenterY;
                        // 2. Apply scaling
                        atomX *= scale;
                        atomY *= scale;
                        // 3. Translate to canvas center
                        atomX += canvasCenterX;
                        atomY += canvasCenterY;

                        // Convert to integer pixel coordinates
                        int pixelX = (int)System.Math.Round(atomX);
                        int pixelY = (int)System.Math.Round(atomY);

                        java.lang.System.err.println("DEBUG: Transformed coordinates: (" + pixelX + ", " + pixelY + ")");

                        // Apply green color to halogen atom area (PRECISE radius to avoid bonds)
                        ApplyHalogenColorToArea(image, pixelX, pixelY, greenRgb, isDarkTheme, 6); // Reduced radius for precision
                    }
                }
            } catch (java.lang.Exception e) {
                java.lang.System.err.println("Error in coordinate-based halogen coloring: " + e.getMessage());
            }
        }

        /// <summary>
        /// Strategy 2: DISABLED - Pixel scanning was too aggressive
        /// Only use coordinate-based approach for precision
        /// </summary>
        private static void ApplyHalogenColorsByPixelScanning(BufferedImage image, IMolecule molecule, int greenRgb, bool isDarkTheme)
        {
            // DISABLED: This approach was affecting bonds and other atoms
            // Only use coordinate-based targeting for precision
            java.lang.System.err.println("DEBUG: Pixel scanning disabled - using coordinate-based approach only");
        }

        /// <summary>
        /// Check if a pixel is likely part of a halogen label
        /// </summary>
        private static bool IsLikelyHalogenLabel(BufferedImage image, int x, int y, bool isDarkTheme)
        {
            try {
                // Simple heuristic: check if there are enough similar pixels nearby (indicating text)
                int width = image.getWidth();
                int height = image.getHeight();
                int radius = 3;
                int similarPixels = 0;

                for (int dx = -radius; dx <= radius; dx++) {
                    for (int dy = -radius; dy <= radius; dy++) {
                        int nx = x + dx;
                        int ny = y + dy;

                        if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                            int nrgb = image.getRGB(nx, ny);
                            int nred = (nrgb >> 16) & 0xFF;
                            int ngreen = (nrgb >> 8) & 0xFF;
                            int nblue = nrgb & 0xFF;

                            bool isTextPixel = false;
                            if (isDarkTheme) {
                                isTextPixel = (nred > 200 && ngreen > 200 && nblue > 200);
                            } else {
                                isTextPixel = (nred < 100 && ngreen < 100 && nblue < 100);
                            }

                            if (isTextPixel) similarPixels++;
                        }
                    }
                }

                // If there are enough similar pixels, it's likely text
                return similarPixels >= 3;
            } catch (java.lang.Exception) {
                return false;
            }
        }

        /// <summary>
        /// Apply green color to halogen atom coordinates with PRECISION targeting
        /// FIXED VERSION: Only targets atom text, not bonds or other elements
        /// </summary>
        private static void ApplyHalogenColorToArea(BufferedImage image, int centerX, int centerY, int greenRgb, bool isDarkTheme, int radius = 6)
        {
            try {
                int width = image.getWidth();
                int height = image.getHeight();

                java.lang.System.err.println("DEBUG: Applying PRECISE halogen color at (" + centerX + ", " + centerY + ") with radius " + radius);

                int pixelsChanged = 0;

                // CRITICAL FIX: Much smaller radius and stricter detection
                for (int dx = -radius; dx <= radius; dx++) {
                    for (int dy = -radius; dy <= radius; dy++) {
                        int x = centerX + dx;
                        int y = centerY + dy;

                        // Check bounds
                        if (x < 0 || x >= width || y < 0 || y >= height) continue;

                        int currentRgb = image.getRGB(x, y);
                        int red = (currentRgb >> 16) & 0xFF;
                        int green = (currentRgb >> 8) & 0xFF;
                        int blue = currentRgb & 0xFF;

                        // CRITICAL FIX: Much stricter atom text detection to avoid bonds
                        bool isAtomText = false;
                        if (isDarkTheme) {
                            // In dark theme, target ONLY pure white text (atom labels)
                            isAtomText = (red >= 240 && green >= 240 && blue >= 240);
                        } else {
                            // In light theme, target ONLY pure black text (atom labels)
                            isAtomText = (red <= 50 && green <= 50 && blue <= 50);
                        }

                        // ADDITIONAL CHECK: Ensure we're not targeting bond pixels
                        if (isAtomText && IsAtomLabelPixel(image, x, y, isDarkTheme)) {
                            int alpha = (currentRgb >> 24) & 0xFF;
                            int newRgb = (alpha << 24) | (greenRgb & 0x00FFFFFF);
                            image.setRGB(x, y, newRgb);
                            pixelsChanged++;
                        }
                    }
                }

                java.lang.System.err.println("DEBUG: Changed " + pixelsChanged + " pixels to green (precise targeting)");

            } catch (java.lang.Exception e) {
                java.lang.System.err.println("Error in ApplyHalogenColorToArea: " + e.getMessage());
                // Fail silently to preserve basic functionality
            }
        }

        /// <summary>
        /// CRITICAL: Verify pixel is part of atom label, not a bond
        /// </summary>
        private static bool IsAtomLabelPixel(BufferedImage image, int x, int y, bool isDarkTheme)
        {
            try {
                int width = image.getWidth();
                int height = image.getHeight();

                // Check immediate neighbors - atom labels have clustered pixels, bonds are linear
                int textPixelCount = 0;
                int linearPixelCount = 0;

                for (int dx = -1; dx <= 1; dx++) {
                    for (int dy = -1; dy <= 1; dy++) {
                        if (dx == 0 && dy == 0) continue;

                        int nx = x + dx;
                        int ny = y + dy;

                        if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
                            int nrgb = image.getRGB(nx, ny);
                            int nred = (nrgb >> 16) & 0xFF;
                            int ngreen = (nrgb >> 8) & 0xFF;
                            int nblue = nrgb & 0xFF;

                            bool isTextPixel = false;
                            if (isDarkTheme) {
                                isTextPixel = (nred >= 240 && ngreen >= 240 && nblue >= 240);
                            } else {
                                isTextPixel = (nred <= 50 && ngreen <= 50 && nblue <= 50);
                            }

                            if (isTextPixel) {
                                textPixelCount++;

                                // Check for linear patterns (indicating bonds)
                                if ((dx == 0 && dy != 0) || (dx != 0 && dy == 0)) {
                                    linearPixelCount++;
                                }
                            }
                        }
                    }
                }

                // Atom labels have clustered pixels, bonds have linear pixels
                // Only target if we have text pixels but not too many linear ones
                return textPixelCount >= 2 && linearPixelCount <= 2;

            } catch (java.lang.Exception) {
                return false;
            }
        }

        /// <summary>
        /// LEGACY HALOGEN COLORING: Enhanced halogen coloring method optimized for light theme
        /// Based on the precise coordinate matching from SmilesConverter_old.cs
        /// </summary>
        private static void ApplyHalogenColorsLegacy(java.awt.image.BufferedImage image, org.openscience.cdk.interfaces.IMolecule molecule,
                                                     double scale, double canvasCenterX, double canvasCenterY, double diagramCenterX, double diagramCenterY, bool isDarkTheme)
        {
            try {
                // Define theme-appropriate green color for halogens (optimized for light theme)
                java.awt.Color halogenGreen = new java.awt.Color(0, 150, 0); // Darker green for light theme
                int greenRgb = halogenGreen.getRGB();

                // Iterate through all atoms in the molecule
                for (int i = 0; i < molecule.getAtomCount(); i++) {
                    var atom = molecule.getAtom(i);
                    if (atom == null) continue;

                    string symbol = atom.getSymbol();

                    // Check if atom is a halogen (F, Cl, Br, I)
                    if (symbol != null && IsHalogenLegacy(symbol)) {
                        // Get atom coordinates
                        var point2d = atom.getPoint2d();
                        if (point2d == null) continue;

                        // Transform coordinates using same approach as legacy method
                        double atomX = point2d.x;
                        double atomY = point2d.y;

                        // Apply the same transformations as the main rendering
                        atomX -= diagramCenterX;
                        atomY -= diagramCenterY;
                        atomX *= scale;
                        atomY *= scale;
                        atomX += canvasCenterX;
                        atomY += canvasCenterY;

                        // Convert to integer pixel coordinates
                        int pixelX = (int)System.Math.Round(atomX);
                        int pixelY = (int)System.Math.Round(atomY);

                        // Apply green color with improved precision for light theme
                        int radius = symbol.Length == 1 ? 5 : 7; // F=5, Cl/Br=7
                        ApplyHalogenColorToAreaLegacy(image, pixelX, pixelY, greenRgb, isDarkTheme, radius);
                    }
                }
            } catch (java.lang.Exception e) {
                java.lang.System.err.println("Error in ApplyHalogenColorsLegacy: " + e.getMessage());
            }
        }

        /// <summary>
        /// Check if an atom symbol represents a halogen element (legacy version)
        /// </summary>
        private static bool IsHalogenLegacy(string symbol)
        {
            return symbol.Equals("F") || symbol.Equals("Cl") || symbol.Equals("Br") || symbol.Equals("I");
        }

        /// <summary>
        /// Apply green color to halogen atom coordinates with PRECISION targeting (legacy version)
        /// Optimized for light theme with white background
        /// </summary>
        private static void ApplyHalogenColorToAreaLegacy(java.awt.image.BufferedImage image, int centerX, int centerY, int greenRgb, bool isDarkTheme, int radius = 6)
        {
            try {
                int width = image.getWidth();
                int height = image.getHeight();

                int pixelsChanged = 0;

                // Use smaller radius and stricter detection for precision
                for (int dx = -radius; dx <= radius; dx++) {
                    for (int dy = -radius; dy <= radius; dy++) {
                        int x = centerX + dx;
                        int y = centerY + dy;

                        // Check bounds
                        if (x < 0 || x >= width || y < 0 || y >= height) continue;

                        int currentRgb = image.getRGB(x, y);
                        int red = (currentRgb >> 16) & 0xFF;
                        int green = (currentRgb >> 8) & 0xFF;
                        int blue = currentRgb & 0xFF;

                        // Stricter atom text detection for light theme (target black text)
                        bool isAtomText = (red <= 50 && green <= 50 && blue <= 50);

                        // Apply green color only to atom text pixels
                        if (isAtomText) {
                            int alpha = (currentRgb >> 24) & 0xFF;
                            int newRgb = (alpha << 24) | (greenRgb & 0x00FFFFFF);
                            image.setRGB(x, y, newRgb);
                            pixelsChanged++;
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Legacy halogen coloring: Changed {pixelsChanged} pixels to green at ({centerX}, {centerY})");

            } catch (java.lang.Exception e) {
                java.lang.System.err.println("Error in ApplyHalogenColorToAreaLegacy: " + e.getMessage());
            }
        }

    }
}
