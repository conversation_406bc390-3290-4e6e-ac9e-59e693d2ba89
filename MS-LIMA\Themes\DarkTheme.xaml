<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Dark Theme Color Palette -->
    <Color x:Key="PrimaryColor">#64B5F6</Color>
    <Color x:Key="PrimaryDarkColor">#1976D2</Color>
    <Color x:Key="PrimaryLightColor">#90CAF9</Color>
    <Color x:Key="AccentColor">#FF4081</Color>

    <!-- Background Colors with proper visual hierarchy -->
    <Color x:Key="WindowBackgroundColor">#1A1A1A</Color>
    <Color x:Key="SurfaceColor">#202020</Color>
    <Color x:Key="CardColor">#282828</Color>
    <Color x:Key="MenuBackgroundColor">#1E1E1E</Color>

    <!-- Accent Colors with dark theme hierarchy -->
    <Color x:Key="LightAccentColor">#303030</Color>
    <Color x:Key="LightAccentLightColor">#383838</Color>
    <Color x:Key="DataGridHeaderColor">#242424</Color>
    <Color x:Key="SectionLabelColor">#303030</Color>

    <!-- Text Colors -->
    <Color x:Key="PrimaryTextColor">#FFFFFF</Color>
    <Color x:Key="SecondaryTextColor">#FFFFFF</Color>  <!-- Kept white for Theme/Color labels -->
    <Color x:Key="DisabledTextColor">#666666</Color>
    <Color x:Key="HintTextColor">#808080</Color>

    <!-- Border and Divider Colors -->
    <Color x:Key="DividerColor">#383838</Color>
    <Color x:Key="BorderColor">#404040</Color>
    <Color x:Key="FocusBorderColor">#64B5F6</Color>

    <!-- State Colors -->
    <Color x:Key="HoverColor">#383838</Color>  <!-- Updated for dark theme -->
    <Color x:Key="PressedColor">#404040</Color>  <!-- Updated for dark theme -->
    <Color x:Key="SelectedColor">#303030</Color>  <!-- Updated for dark theme -->

    <!-- Alternating Row Colors -->
    <Color x:Key="AlternatingRowColor">#252525</Color>
    <Color x:Key="ErrorColor">#F44336</Color>
    <Color x:Key="WarningColor">#FF9800</Color>
    <Color x:Key="SuccessColor">#4CAF50</Color>

    <!-- Convert Colors to Brushes -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>

    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="{StaticResource WindowBackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="CardBrush" Color="{StaticResource CardColor}"/>
    <SolidColorBrush x:Key="MenuBackgroundBrush" Color="{StaticResource MenuBackgroundColor}"/>

    <SolidColorBrush x:Key="PrimaryTextBrush" Color="{StaticResource PrimaryTextColor}"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="{StaticResource SecondaryTextColor}"/>
    <SolidColorBrush x:Key="DisabledTextBrush" Color="{StaticResource DisabledTextColor}"/>
    <SolidColorBrush x:Key="HintTextBrush" Color="{StaticResource HintTextColor}"/>

    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="FocusBorderBrush" Color="{StaticResource FocusBorderColor}"/>

    <!-- Theme-aware brushes - will be updated dynamically by ThemeService -->
    <SolidColorBrush x:Key="HoverBrush" Color="{StaticResource HoverColor}"/>
    <SolidColorBrush x:Key="PressedBrush" Color="{StaticResource PressedColor}"/>
    <SolidColorBrush x:Key="SelectedBrush" Color="{StaticResource SelectedColor}"/>
    <SolidColorBrush x:Key="SelectedRowBrush" Color="{StaticResource SelectedColor}"/>

    <!-- Alternating Row Brush for Banded Tables -->
    <SolidColorBrush x:Key="AlternatingRowBrush" Color="{StaticResource AlternatingRowColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>

    <!-- Dark Theme Gradient Brushes with tag highlight color -->
    <LinearGradientBrush x:Key="DataGridHeaderGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#242424" Offset="0"/>
        <GradientStop Color="#2A2A2A" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="SectionLabelGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#303030" Offset="0"/>
        <GradientStop Color="#383838" Offset="1"/>
    </LinearGradientBrush>

    <!-- Color Scheme Selector Brush (Slightly lighter than table headers) -->
    <LinearGradientBrush x:Key="ColorSchemeSelectorBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#454545" Offset="0"/>
        <GradientStop Color="#4A4A4A" Offset="1"/>
    </LinearGradientBrush>

    <!-- Typography -->
    <FontFamily x:Key="PrimaryFontFamily">Segoe UI</FontFamily>
    <FontFamily x:Key="MonospaceFontFamily">Consolas</FontFamily>

    <!-- Font Sizes -->
<!-- Grey background for combo text - dark mode -->
    <SolidColorBrush x:Key="ComboTextBackgroundBrush" Color="#404040"/>
    <sys:Double x:Key="HeaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    <sys:Double x:Key="SubheaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="BodyFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
    <sys:Double x:Key="CaptionFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>
    <sys:Double x:Key="SmallFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">10</sys:Double>

    <!-- Spacing and Sizing -->
    <Thickness x:Key="DefaultMargin">8</Thickness>
    <Thickness x:Key="SmallMargin">4</Thickness>
    <Thickness x:Key="LargeMargin">16</Thickness>
    <Thickness x:Key="DefaultPadding">12,8</Thickness>
    <Thickness x:Key="SmallPadding">8,4</Thickness>
    <Thickness x:Key="LargePadding">16,12</Thickness>

    <CornerRadius x:Key="DefaultCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="SmallCornerRadius">2</CornerRadius>
    <CornerRadius x:Key="LargeCornerRadius">8</CornerRadius>

    <!-- Shadows -->
    <DropShadowEffect x:Key="DefaultShadow" Color="Black" Opacity="0.3" BlurRadius="8" ShadowDepth="2" Direction="270"/>
    <DropShadowEffect x:Key="ElevatedShadow" Color="Black" Opacity="0.4" BlurRadius="12" ShadowDepth="4" Direction="270"/>

</ResourceDictionary>
