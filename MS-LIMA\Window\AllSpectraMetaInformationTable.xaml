<Window x:Class="Metabolomics.MsLima.AllSpectraMetaInformationTable"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:Metabolomics.MsLima"
        mc:Ignorable="d"
        Title="Meta information of all spectra" Height="500" Width="1220">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Filter row -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Name="FilterPanel">
            <TextBox Width="50" Margin="2" Text="{Binding ColumnFilterValues[ID], UpdateSourceTrigger=PropertyChanged}"/>
            <TextBox Width="2*" Margin="2" Text="{Binding ColumnFilterValues[Name], UpdateSourceTrigger=PropertyChanged}"/>
            <TextBox Width="50" Margin="2" Text="{Binding ColumnFilterValues[Retention time], UpdateSourceTrigger=PropertyChanged}"/>
            <TextBox Width="80" Margin="2" Text="{Binding ColumnFilterValues[m/z], UpdateSourceTrigger=PropertyChanged}"/>
            <!-- Additional filter boxes would go here matching the DataGrid columns -->
        </StackPanel>

        <DataGrid Grid.Row="1" Name="DataGrid_RawData" ItemsSource="{Binding Path=Table}" CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False" CanUserSortColumns="True" SelectionUnit="FullRow" IsReadOnly="False" HeadersVisibility="All" AutoGenerateColumns="False" ColumnWidth="*" Margin="0,0,0,0" ScrollViewer.HorizontalScrollBarVisibility="Auto" ScrollViewer.VerticalScrollBarVisibility="Auto">
            <DataGrid.ColumnHeaderStyle>
                <Style TargetType="DataGridColumnHeader" >
                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                </Style>
            </DataGrid.ColumnHeaderStyle>
            <DataGrid.Columns>
                <DataGridTextColumn Header="ID" Width="50" Binding="{Binding Path=Id}" IsReadOnly="False" />
                <DataGridTextColumn Header="Name" Width="2*" Binding="{Binding Path=Name}" IsReadOnly="False" />
                <DataGridTextColumn Header="Retention time" Width="50" Binding="{Binding Path=RetentionTime, StringFormat=0.00}" IsReadOnly="False" />
                <DataGridTextColumn Header="m/z" Width="80" Binding="{Binding Path=PrecursorMz, StringFormat=0.000}" IsReadOnly="False" />
                <DataGridTextColumn Header="Theor. m/z" Width="80" Binding="{Binding Path=TheoreticalMass, StringFormat=0.000}" IsReadOnly="False" />
                <DataGridTextColumn Header="Diff (ppm)" Width="80" Binding="{Binding Path=DiffPpm, StringFormat=0.0}" IsReadOnly="False" />
                <DataGridTextColumn Header="Type" Width="80" Binding="{Binding Path=AdductIon.AdductIonName}" IsReadOnly="False" />
                <DataGridTextColumn Header="Collision Energy" Width="50" Binding="{Binding Path=CollisionEnergy}" IsReadOnly="False" />
                <DataGridTextColumn Header="Formula" Width="100" Binding="{Binding Path=Formula}" IsReadOnly="False" />
                <DataGridTextColumn Header="InChIKey" Width="100" Binding="{Binding Path=InChIKey}" IsReadOnly="False" />
                <DataGridTextColumn Header="NumPeaks" Width="50" Binding="{Binding Path=PeakNumber}" IsReadOnly="True" />
                <DataGridTextColumn Header="Comment" Width="2*" Binding="{Binding Path=Comment}" IsReadOnly="false" />
                <DataGridTextColumn Header="IUPAC Name" Width="2*" Binding="{Binding Path=IUPACname}" IsReadOnly="False" />
                <DataGridTextColumn Header="CID" Width="80" Binding="{Binding Path=CID}" IsReadOnly="False" />
                <DataGridTextColumn Header="CAS" Width="100" Binding="{Binding Path=CAS}" IsReadOnly="False" />
                <DataGridTextColumn Header="Exact Mass" Width="80" Binding="{Binding Path=Exactmass, StringFormat=0.000}" IsReadOnly="False" />
                <DataGridTextColumn Header="MW" Width="80" Binding="{Binding Path=MW, StringFormat=0.000}" IsReadOnly="False" />
                <DataGridTextColumn Header="XLogP" Width="60" Binding="{Binding Path=XLogP, StringFormat=0.00}" IsReadOnly="False" />
                <DataGridTextColumn Header="MLogP" Width="60" Binding="{Binding Path=MLogP, StringFormat=0.00}" IsReadOnly="False" />
                <DataGridTextColumn Header="ALogP" Width="60" Binding="{Binding Path=ALogP, StringFormat=0.00}" IsReadOnly="False" />
                <DataGridTextColumn Header="NumBond" Width="60" Binding="{Binding Path=NumBond}" IsReadOnly="False" />
                <DataGridTextColumn Header="NumAtom" Width="60" Binding="{Binding Path=NumAtom}" IsReadOnly="False" />
                <DataGridTextColumn Header="Superclass" Width="100" Binding="{Binding Path=Superclass}" IsReadOnly="False" />
                <DataGridTextColumn Header="Class" Width="100" Binding="{Binding Path=Class}" IsReadOnly="False" />
                <DataGridTextColumn Header="Subclass" Width="100" Binding="{Binding Path=Subclass}" IsReadOnly="False" />
                <DataGridTextColumn Header="Cramer Rules" Width="100" Binding="{Binding Path=Cramerrules}" IsReadOnly="False" />
                <DataGridTextColumn Header="SVHC" Width="80" Binding="{Binding Path=SVHC}" IsReadOnly="False" />
                <DataGridTextColumn Header="CMR" Width="80" Binding="{Binding Path=CMR}" IsReadOnly="False" />
                <DataGridTextColumn Header="CMR Suspect" Width="100" Binding="{Binding Path=CMRSuspect}" IsReadOnly="False" />
                <DataGridTextColumn Header="EDC" Width="80" Binding="{Binding Path=EDC}" IsReadOnly="False" />
                <DataGridTextColumn Header="IARC" Width="80" Binding="{Binding Path=IARC}" IsReadOnly="False" />
                <DataGridTextColumn Header="EUSML" Width="80" Binding="{Binding Path=Eusml}" IsReadOnly="False" />
                <DataGridTextColumn Header="ChinaSML" Width="80" Binding="{Binding Path=ChinaSml}" IsReadOnly="False" />
                <DataGridTextColumn Header="Ontology" Width="100" Binding="{Binding Path=Ontology}" IsReadOnly="False" />
            </DataGrid.Columns>
        </DataGrid>

    </Grid>
</Window>
