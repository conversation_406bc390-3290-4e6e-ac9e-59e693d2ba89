﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Metabolomics.MsLima.Services;
using Metabolomics.Core;
using ChartDrawing.Services;

namespace Metabolomics.MsLima
{
    /// <summary>
    /// App.xaml の相互作用ロジック
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // THEME-AWARE SPECTRA PLOTS: Initialize theme provider service for charts from startup
            ThemeProviderService.SetProvider(ThemeService.Instance);

            // Initialize theme manager
            ThemeManager.Instance.Initialize();

            // Ensure ThemeService resources are initialized
            ThemeService.Instance.InitializeThemeResources();

            // Synchronize theme service with theme manager
            ThemeService.Instance.IsDarkTheme = ThemeManager.Instance.IsDarkTheme;

            // THEME-SPECIFIC STRUCTURE RENDERING: Set up theme detection delegate for SmilesConverter
            SmilesConverter.IsDarkThemeFunc = () => ThemeManager.Instance.IsDarkTheme;

            System.Diagnostics.Debug.WriteLine($"App startup: Theme provider initialized, current theme: {(ThemeManager.Instance.IsDarkTheme ? "Dark" : "Light")}");
        }
    }
}
