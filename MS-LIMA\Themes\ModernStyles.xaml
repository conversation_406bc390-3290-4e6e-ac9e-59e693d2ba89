<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Modern Button Style -->
    <Style x:Key="ModernButton" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="{DynamicResource DefaultPadding}"/>
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Effect" Value="{DynamicResource DefaultShadow}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{DynamicResource DefaultCornerRadius}"
                            SnapsToDevicePixels="True">
                        <ContentPresenter x:Name="contentPresenter"
                                        Focusable="False"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        Margin="{TemplateBinding Padding}"
                                        RecognizesAccessKey="True"
                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsDefaulted" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="{DynamicResource FocusBorderBrush}"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{DynamicResource PrimaryDarkBrush}"/>
                            <Setter Property="Effect" Value="{DynamicResource ElevatedShadow}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{DynamicResource PrimaryDarkBrush}"/>
                            <Setter Property="Effect" Value="{x:Null}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" TargetName="border" Value="{DynamicResource DisabledTextBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource HintTextBrush}"/>
                            <Setter Property="Effect" Value="{x:Null}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Secondary Button Style -->
    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource ModernButton}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
        <Setter Property="Effect" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{DynamicResource DefaultCornerRadius}"
                            SnapsToDevicePixels="True">
                        <ContentPresenter x:Name="contentPresenter"
                                        Focusable="False"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        Margin="{TemplateBinding Padding}"
                                        RecognizesAccessKey="True"
                                        SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{DynamicResource HoverBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{DynamicResource PressedBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="BorderBrush" TargetName="border" Value="{DynamicResource DisabledTextBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource DisabledTextBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Icon Button Style -->
    <Style x:Key="IconButton" TargetType="Button" BasedOn="{StaticResource SecondaryButton}">
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{DynamicResource SecondaryTextBrush}"/>
    </Style>

    <!-- Modern TextBox Style -->
    <Style x:Key="ModernTextBox" TargetType="TextBox">
        <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="{DynamicResource DefaultPadding}"/>
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{DynamicResource DefaultCornerRadius}"
                            SnapsToDevicePixels="True">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Focusable="False"
                                    HorizontalScrollBarVisibility="Hidden"
                                    VerticalScrollBarVisibility="Hidden"
                                    VerticalAlignment="Center"
                                    Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" TargetName="border" Value="0.56"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="{DynamicResource FocusBorderBrush}"/>
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="{DynamicResource FocusBorderBrush}"/>
                            <Setter Property="BorderThickness" TargetName="border" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern Menu Style -->
    <Style x:Key="ModernMenu" TargetType="Menu">
        <Setter Property="Background" Value="{DynamicResource MenuBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="BorderBrush" Value="{DynamicResource DividerBrush}"/>
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}"/>
        <Setter Property="Padding" Value="2,8"/>
        <Setter Property="MinHeight" Value="50"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>

    <!-- Modern MenuItem Style -->
    <Style x:Key="ModernMenuItem" TargetType="MenuItem">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
        <Setter Property="Padding" Value="5,12"/>
        <Setter Property="MinHeight" Value="40"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="MenuItem">
                    <Grid>
                        <Border x:Name="templateRoot"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                SnapsToDevicePixels="True">
                            <Grid Margin="-1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition MinWidth="22" SharedSizeGroup="MenuItemIconColumnGroup" Width="Auto"/>
                                    <ColumnDefinition Width="13"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="30"/>
                                    <ColumnDefinition SharedSizeGroup="MenuItemIGTColumnGroup" Width="Auto"/>
                                    <ColumnDefinition Width="20"/>
                                </Grid.ColumnDefinitions>
                                <ContentPresenter x:Name="Icon"
                                                ContentSource="Icon"
                                                HorizontalAlignment="Center"
                                                Height="16"
                                                Margin="3"
                                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                                VerticalAlignment="Center"
                                                Width="16"/>
                                <Border x:Name="GlyphPanel"
                                        Background="Transparent"
                                        BorderBrush="Transparent"
                                        BorderThickness="1"
                                        ClipToBounds="False"
                                        HorizontalAlignment="Center"
                                        Height="22"
                                        Margin="-1,0,0,0"
                                        Visibility="Hidden"
                                        VerticalAlignment="Center"
                                        Width="22">
                                    <Path x:Name="Glyph"
                                        Data="F1 M 10.0,1.2 L 4.7,9.1 L 4.5,9.1 L 0,5.2 L 1.3,3.5 L 4.3,6.1L 8.3,0 L 10.0,1.2 Z"
                                        Fill="{DynamicResource PrimaryTextBrush}"
                                        FlowDirection="LeftToRight"
                                        Height="11"
                                        Width="9"/>
                                </Border>
                                <ContentPresenter x:Name="menuHeaderContainer"
                                                ContentSource="Header"
                                                Grid.Column="2"
                                                HorizontalAlignment="Left"
                                                Margin="{TemplateBinding Padding}"
                                                RecognizesAccessKey="True"
                                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                                VerticalAlignment="Center"
                                                TextElement.Foreground="{TemplateBinding Foreground}"/>
                                <TextBlock x:Name="menuGestureText"
                                         Grid.Column="4"
                                         Margin="{TemplateBinding Padding}"
                                         Opacity="0.7"
                                         Text="{TemplateBinding InputGestureText}"
                                         VerticalAlignment="Center"
                                         Foreground="{TemplateBinding Foreground}"/>
                            </Grid>
                        </Border>
                        <Popup x:Name="PART_Popup"
                               AllowsTransparency="True"
                               Focusable="False"
                               IsOpen="{Binding IsSubmenuOpen, RelativeSource={RelativeSource TemplatedParent}}"
                               Placement="Bottom"
                               PopupAnimation="Fade"
                               StaysOpen="False">
                            <Border x:Name="SubMenuBorder"
                                    Background="{DynamicResource SurfaceBrush}"
                                    BorderBrush="{DynamicResource DividerBrush}"
                                    BorderThickness="1"
                                    CornerRadius="8"
                                    Margin="0,4,0,0"
                                    Effect="{DynamicResource ModernShadow}">
                                <ScrollViewer x:Name="SubMenuScrollViewer" Style="{DynamicResource {ComponentResourceKey ResourceId=MenuScrollViewer, TypeInTargetAssembly={x:Type FrameworkElement}}}">
                                    <Grid RenderOptions.ClearTypeHint="Enabled">
                                        <Canvas HorizontalAlignment="Left" Height="0" VerticalAlignment="Top" Width="0">
                                            <Rectangle x:Name="OpaqueRect" Fill="{Binding Background, ElementName=SubMenuBorder}" Height="{Binding ActualHeight, ElementName=SubMenuBorder}" Width="{Binding ActualWidth, ElementName=SubMenuBorder}"/>
                                        </Canvas>
                                        <ItemsPresenter x:Name="ItemsPresenter" KeyboardNavigation.DirectionalNavigation="Cycle" Grid.IsSharedSizeScope="True" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" KeyboardNavigation.TabNavigation="Cycle"/>
                                    </Grid>
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Icon" Value="{x:Null}">
                            <Setter Property="Visibility" TargetName="Icon" Value="Collapsed"/>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter Property="Visibility" TargetName="GlyphPanel" Value="Visible"/>
                            <Setter Property="Visibility" TargetName="Icon" Value="Collapsed"/>
                        </Trigger>
                        <Trigger Property="IsHighlighted" Value="True">
                            <Setter Property="Background" TargetName="templateRoot" Value="{DynamicResource HoverBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="TextElement.Foreground" TargetName="templateRoot" Value="{DynamicResource DisabledTextBrush}"/>
                            <Setter Property="Fill" TargetName="Glyph" Value="{DynamicResource DisabledTextBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern DataGrid Style -->
    <Style x:Key="ModernDataGrid" TargetType="DataGrid">
        <Setter Property="Background" Value="{DynamicResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="RowDetailsVisibilityMode" Value="VisibleWhenSelected"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
        <Setter Property="ScrollViewer.PanningMode" Value="Both"/>
        <Setter Property="Stylus.IsFlicksEnabled" Value="False"/>
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}"/>
        <Setter Property="GridLinesVisibility" Value="None"/>
        <Setter Property="RowBackground" Value="Transparent"/>
        <Setter Property="AlternatingRowBackground" Value="{DynamicResource AlternatingRowBrush}"/>
        <!-- CRITICAL: Override default selection colors with theme-aware brushes -->
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="SelectionUnit" Value="FullRow"/>
    </Style>

    <!-- Modern DataGridColumnHeader Style -->
    <Style x:Key="ModernDataGridColumnHeader" TargetType="DataGridColumnHeader">
        <Setter Property="Background" Value="{DynamicResource DataGridHeaderGradientBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource BodyFontSize}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
    </Style>

    <!-- Modern DataGridRow Style -->
    <Style x:Key="ModernDataGridRow" TargetType="DataGridRow">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <!-- CRITICAL: Override default template to ensure our selection colors work -->
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridRow">
                    <Border x:Name="DGR_Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
                        <SelectiveScrollingGrid>
                            <SelectiveScrollingGrid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </SelectiveScrollingGrid.ColumnDefinitions>
                            <SelectiveScrollingGrid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </SelectiveScrollingGrid.RowDefinitions>
                            <DataGridCellsPresenter Grid.Column="1"
                                                   ItemsPanel="{TemplateBinding ItemsPanel}"
                                                   SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            <DataGridDetailsPresenter Grid.Column="1" Grid.Row="1"
                                                     SelectiveScrollingGrid.SelectiveScrollingOrientation="{Binding AreRowDetailsFrozen, ConverterParameter={x:Static SelectiveScrollingOrientation.Vertical}, Converter={x:Static DataGrid.RowDetailsScrollingConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                                                     Visibility="{TemplateBinding DetailsVisibility}"/>
                            <DataGridRowHeader Grid.RowSpan="2"
                                             SelectiveScrollingGrid.SelectiveScrollingOrientation="Vertical"
                                             Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Row}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"/>
                        </SelectiveScrollingGrid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- Alternating row background -->
                        <Trigger Property="ItemsControl.AlternationIndex" Value="1">
                            <Setter TargetName="DGR_Border" Property="Background" Value="{DynamicResource AlternatingRowBrush}"/>
                        </Trigger>
                        <!-- Mouse over effect -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="DGR_Border" Property="Background" Value="{DynamicResource HoverBrush}"/>
                        </Trigger>
                        <!-- CRITICAL: Selection effect with highest priority -->
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="DGR_Border" Property="Background" Value="{DynamicResource SelectedRowBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Modern ScrollBar Style -->
    <Style x:Key="ModernScrollBar" TargetType="ScrollBar">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Width" Value="12"/>
        <Setter Property="MinWidth" Value="12"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Grid x:Name="Bg" SnapsToDevicePixels="True">
                        <Track x:Name="PART_Track" IsDirectionReversed="True" IsEnabled="{TemplateBinding IsMouseOver}">
                            <Track.DecreaseRepeatButton>
                                <RepeatButton Command="{x:Static ScrollBar.PageUpCommand}" Style="{DynamicResource ScrollBarPageButton}"/>
                            </Track.DecreaseRepeatButton>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton Command="{x:Static ScrollBar.PageDownCommand}" Style="{DynamicResource ScrollBarPageButton}"/>
                            </Track.IncreaseRepeatButton>
                            <Track.Thumb>
                                <Thumb Style="{DynamicResource ScrollBarThumb}"/>
                            </Track.Thumb>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter Property="Width" Value="Auto"/>
                <Setter Property="MinWidth" Value="0"/>
                <Setter Property="Height" Value="12"/>
                <Setter Property="MinHeight" Value="12"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ScrollBar">
                            <Grid x:Name="Bg" SnapsToDevicePixels="True">
                                <Track x:Name="PART_Track" IsEnabled="{TemplateBinding IsMouseOver}">
                                    <Track.DecreaseRepeatButton>
                                        <RepeatButton Command="{x:Static ScrollBar.PageLeftCommand}" Style="{DynamicResource ScrollBarPageButton}"/>
                                    </Track.DecreaseRepeatButton>
                                    <Track.IncreaseRepeatButton>
                                        <RepeatButton Command="{x:Static ScrollBar.PageRightCommand}" Style="{DynamicResource ScrollBarPageButton}"/>
                                    </Track.IncreaseRepeatButton>
                                    <Track.Thumb>
                                        <Thumb Style="{DynamicResource ScrollBarThumb}"/>
                                    </Track.Thumb>
                                </Track>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- ScrollBar Thumb Style -->
    <Style x:Key="ScrollBarThumb" TargetType="Thumb">
        <Setter Property="Background" Value="{DynamicResource SecondaryTextBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Thumb">
                    <Border x:Name="rectangle"
                            Background="{TemplateBinding Background}"
                            CornerRadius="6"
                            Opacity="0.5"
                            SnapsToDevicePixels="True"/>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Opacity" TargetName="rectangle" Value="0.8"/>
                        </Trigger>
                        <Trigger Property="IsDragging" Value="True">
                            <Setter Property="Opacity" TargetName="rectangle" Value="1"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ScrollBar Page Button Style -->
    <Style x:Key="ScrollBarPageButton" TargetType="RepeatButton">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RepeatButton">
                    <Rectangle Fill="Transparent"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
