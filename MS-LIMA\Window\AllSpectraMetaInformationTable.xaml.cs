﻿using System.Windows;
using System.Windows.Controls;
using Metabolomics.MsLima.ViewModel;

namespace Metabolomics.MsLima
{
    /// <summary>
    /// AllSpectraMetaInformationTable.xaml の相互作用ロジック
    /// </summary>
    public partial class AllSpectraMetaInformationTable : Window
    {
        public AllSpectraMetaInformationTable(MsLimaData data)
        {
            InitializeComponent();
            this.DataContext = new AllSpectraMetaInformationTableVM(data);
            DataGrid_RawData.Loaded += (s, e) => FreezeColumnWidths();
            DataGrid_RawData.DataContextChanged += (s, e) => FreezeColumnWidths();
        }

        private void FreezeColumnWidths()
        {
            if (DataGrid_RawData.Columns == null) return;
            
            foreach (var column in DataGrid_RawData.Columns)
            {
                // Convert any auto or star sizing to fixed pixel values
                if (column.Width.IsAuto || column.Width.IsStar)
                {
                    column.Width = new DataGridLength(column.ActualWidth, DataGridLengthUnitType.Pixel);
                }
            }
        }
    }
}
