using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Media;
using ChartDrawing.Services;

namespace Metabolomics.MsLima.Services
{
    /// <summary>
    /// Base theme modes (light/dark)
    /// </summary>
    public enum BaseTheme
    {
        Light,
        Dark
    }

    /// <summary>
    /// Available color schemes for accent colors - Modern Professional Palette
    /// </summary>
    public enum ColorScheme
    {
        MaterialBlue,
        MaterialIndigo,
        MaterialTeal,
        MaterialGreen,
        VSCodeDarkBlue,
        VSCodePurple,
        VSCodeOrange,
        GitHubGrayBlue,
        ModernCoral,
        ModernMint,
        LightPink,
        ModCyanate
    }
    public class ThemeService : IThemeProvider
    {
        private static readonly Lazy<ThemeService> _instance = new Lazy<ThemeService>(() => new ThemeService());
        public static ThemeService Instance => _instance.Value;

        public event EventHandler ThemeChanged;
        public event EventHandler<BaseTheme> BaseThemeChanged;
        public event EventHandler<ColorScheme> ColorSchemeChanged;

        private BaseTheme _baseTheme = BaseTheme.Light;
        private ColorScheme _colorScheme = ColorScheme.MaterialBlue;

        public BaseTheme CurrentBaseTheme
        {
            get => _baseTheme;
            set
            {
                if (_baseTheme != value)
                {
                    _baseTheme = value;

                    // Force refresh of all color-dependent properties when theme changes
                    RefreshColorDependentProperties();

                    BaseThemeChanged?.Invoke(this, value);
                    ThemeChanged?.Invoke(this, EventArgs.Empty);

                    System.Diagnostics.Debug.WriteLine($"Base theme changed to {value} - all properties refreshed");
                }
            }
        }

        public ColorScheme CurrentColorScheme
        {
            get => _colorScheme;
            set
            {
                if (_colorScheme != value)
                {
                    _colorScheme = value;

                    // Force refresh of all color-dependent properties
                    RefreshColorDependentProperties();

                    // Notify all listeners
                    ColorSchemeChanged?.Invoke(this, value);
                    ThemeChanged?.Invoke(this, EventArgs.Empty);

                    System.Diagnostics.Debug.WriteLine($"Color scheme changed to {value} - all properties refreshed");
                }
            }
        }

        // Backward compatibility property
        public bool IsDarkTheme
        {
            get => _baseTheme == BaseTheme.Dark;
            set
            {
                var newBaseTheme = value ? BaseTheme.Dark : BaseTheme.Light;
                if (_baseTheme != newBaseTheme)
                {
                    CurrentBaseTheme = newBaseTheme;
                }
            }
        }

        // Chart Background Colors
        public Brush ChartBackgroundBrush => IsDarkTheme
            ? new SolidColorBrush(Color.FromRgb(0x1E, 0x1E, 0x1E))
            : Brushes.White;

        public Brush ChartSurfaceBrush => IsDarkTheme
            ? new SolidColorBrush(Color.FromRgb(0x2D, 0x2D, 0x2D))
            : new SolidColorBrush(Color.FromRgb(0xF5, 0xF5, 0xF5));

        // Chart Text Colors
        public Brush ChartTextBrush => IsDarkTheme
            ? Brushes.White
            : Brushes.Black;

        public Brush ChartSecondaryTextBrush => IsDarkTheme
            ? new SolidColorBrush(Color.FromRgb(0xB3, 0xB3, 0xB3))
            : new SolidColorBrush(Color.FromRgb(0x66, 0x66, 0x66));

        // Chart Grid and Border Colors
        public Brush ChartGridBrush => IsDarkTheme
            ? new SolidColorBrush(Color.FromRgb(0x44, 0x44, 0x44))
            : new SolidColorBrush(Color.FromRgb(0xE0, 0xE0, 0xE0));

        public Pen ChartGridPen => new Pen(ChartGridBrush, 0.5);

        public Pen ChartBorderPen => new Pen(ChartGridBrush, 1.0);

        // Chart Data Colors - Now uses color scheme with caching
        public Brush ChartDataBrush => _chartDataBrush ?? (_chartDataBrush = GetAccentBrush(IsDarkTheme));

        public Pen ChartDataPen => new Pen(ChartDataBrush, 1.0);

        // Mass Spectrum Bar Colors - Now uses color scheme with caching
        public Brush ChartSpectrumBrush => _chartSpectrumBrush ?? (_chartSpectrumBrush = GetAccentBrush(IsDarkTheme));

        public Pen ChartSpectrumPen => new Pen(ChartSpectrumBrush, 1.0);

        public Pen ChartSpectrumPenThick => new Pen(ChartSpectrumBrush, 2.0);

        // Highlight Colors
        public Brush ChartHighlightBrush => new SolidColorBrush(Color.FromRgb(0xFF, 0x40, 0x81));
        public Pen ChartHighlightPen => new Pen(ChartHighlightBrush, 2.5);

        // Consensus Colors
        public Pen ChartConsensusPenLow => IsDarkTheme
            ? new Pen(ChartTextBrush, 1)
            : new Pen(Brushes.Black, 1);

        public Pen ChartConsensusPenMedium => new Pen(new SolidColorBrush(Color.FromRgb(0x21, 0x96, 0xF3)), 1);
        public Pen ChartConsensusPenHigh => new Pen(new SolidColorBrush(Color.FromRgb(0xFF, 0x40, 0x81)), 1);

        // Label Background
        public Brush ChartLabelBackgroundBrush => IsDarkTheme
            ? Utility.CombineAlphaAndColor(0.9, new SolidColorBrush(Color.FromRgb(0x2D, 0x2D, 0x2D)))
            : Utility.CombineAlphaAndColor(0.9, Brushes.White);

        private ThemeService()
        {
            // Initialize theme-aware resources immediately
            InitializeThemeResources();
        }

        /// <summary>
        /// Initialize theme-aware resources to ensure they exist from startup
        /// </summary>
        public void InitializeThemeResources()
        {
            try
            {
                Application.Current?.Dispatcher.Invoke(() =>
                {
                    // Force initial update of all theme resources
                    RefreshColorDependentProperties();
                    System.Diagnostics.Debug.WriteLine("Theme resources initialized successfully");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing theme resources: {ex.Message}");
            }
        }

        /// <summary>
        /// Get accent brush based on current color scheme and theme
        /// </summary>
        public Brush GetAccentBrush(bool isDark = false)
        {
            var color = GetAccentColor(_colorScheme);
            if (isDark)
            {
                // Darken the color slightly for dark themes instead of lightening
                return new SolidColorBrush(DarkenColor(color, 0.1f));
            }
            return new SolidColorBrush(color);
        }

        /// <summary>
        /// Get accent color for the current color scheme - Modern Professional Palette
        /// </summary>
        public Color GetAccentColor(ColorScheme scheme)
        {
            switch (scheme)
            {
                case ColorScheme.MaterialBlue:
                    return Color.FromRgb(0x21, 0x96, 0xF3); // Material Blue
                case ColorScheme.MaterialIndigo:
                    return Color.FromRgb(0x3F, 0x51, 0xB5); // Material Indigo
                case ColorScheme.MaterialTeal:
                    return Color.FromRgb(0x00, 0x96, 0x88); // Material Teal
                case ColorScheme.MaterialGreen:
                    return Color.FromRgb(0x4C, 0xAF, 0x50); // Material Green
                case ColorScheme.VSCodeDarkBlue:
                    return Color.FromRgb(0x00, 0x7A, 0xCC); // VS Code Dark Blue
                case ColorScheme.VSCodePurple:
                    return Color.FromRgb(0x68, 0x21, 0x7A); // VS Code Purple
                case ColorScheme.VSCodeOrange:
                    return Color.FromRgb(0xFF, 0x8C, 0x00); // VS Code Orange
                case ColorScheme.GitHubGrayBlue:
                    return Color.FromRgb(0x58, 0x6C, 0x82); // GitHub Gray-Blue
                case ColorScheme.ModernCoral:
                    return Color.FromRgb(0xFF, 0x6B, 0x6B); // Modern Coral
                case ColorScheme.ModernMint:
                    return Color.FromRgb(0x51, 0xCF, 0x66); // Modern Mint
                case ColorScheme.LightPink:
                    return Color.FromRgb(0xF1, 0x43, 0x7C); // Light Pink
                case ColorScheme.ModCyanate:
                    return Color.FromRgb(0x61, 0xCF, 0xC3); // Modern Cyan-Teal
                default:
                    return Color.FromRgb(0x21, 0x96, 0xF3); // Default Material Blue
            }
        }

        /// <summary>
        /// Get display name for color scheme - Modern Professional Palette
        /// </summary>
        public static string GetColorSchemeDisplayName(ColorScheme scheme)
        {
            switch (scheme)
            {
                case ColorScheme.MaterialBlue:
                    return "Material Blue";
                case ColorScheme.MaterialIndigo:
                    return "Material Indigo";
                case ColorScheme.MaterialTeal:
                    return "Material Teal";
                case ColorScheme.MaterialGreen:
                    return "Material Green";
                case ColorScheme.VSCodeDarkBlue:
                    return "VS Code Blue";
                case ColorScheme.VSCodePurple:
                    return "VS Code Purple";
                case ColorScheme.VSCodeOrange:
                    return "VS Code Orange";
                case ColorScheme.GitHubGrayBlue:
                    return "GitHub Gray";
                case ColorScheme.ModernCoral:
                    return "Modern Coral";
                case ColorScheme.ModernMint:
                    return "Modern Mint";
                case ColorScheme.LightPink:
                    return "Light Pink";
                case ColorScheme.ModCyanate:
                    return "Mod Cyanate";
                default:
                    return "Material Blue";
            }
        }

        /// <summary>
        /// Lighten a color by a specified factor
        /// </summary>
        private Color LightenColor(Color color, float factor)
        {
            var r = Math.Min(255, color.R + (int)((255 - color.R) * factor));
            var g = Math.Min(255, color.G + (int)((255 - color.G) * factor));
            var b = Math.Min(255, color.B + (int)((255 - color.B) * factor));
            return Color.FromRgb((byte)r, (byte)g, (byte)b);
        }

        /// <summary>
        /// Darken a color by a specified factor
        /// </summary>
        private Color DarkenColor(Color color, float factor)
        {
            var r = Math.Max(0, color.R - (int)(color.R * factor));
            var g = Math.Max(0, color.G - (int)(color.G * factor));
            var b = Math.Max(0, color.B - (int)(color.B * factor));
            return Color.FromRgb((byte)r, (byte)g, (byte)b);
        }

        /// <summary>
        /// Create menu background color for resource updates
        /// </summary>
        private Color GetMenuBackgroundColor(Color accentColor, bool isDark)
        {
            if (isDark)
            {
                // In dark mode, make menu color even darker than table headers
                var menuColor = DarkenColor(accentColor, 0.5f);
                menuColor.A = 255;
                return menuColor;
            }
            else
            {
                // Make light mode menu colors brighter
                var menuColor = LightenColor(accentColor, 0.8f); // Increased from 0.6f to 0.8f
                menuColor.A = 255;
                return menuColor;
            }
        }

        /// <summary>
        /// Create menu background brush based on color scheme and theme - VISUAL HIERARCHY ENHANCEMENT
        /// </summary>
        private Brush CreateMenuBackgroundBrush(Color accentColor, bool isDark)
        {
            return new SolidColorBrush(GetMenuBackgroundColor(accentColor, isDark));
        }

        /// <summary>
        /// Create table header gradient brush based on color scheme and theme - SPECTRUM PLOT TAG COLOR MATCHING
        /// </summary>
        private Brush CreateTableHeaderGradientBrush(Color accentColor, bool isDark)
        {
            if (isDark)
            {
                // FIXED: Dark mode now creates LinearGradientBrush for consistency with theme controls
                // Use even darker colors for better visual hierarchy with menu bar
                var spectrumMatchingColor = DarkenColor(accentColor, 0.5f);
                var slightlyLighterColor = DarkenColor(accentColor, 0.4f);

                var gradientBrush = new LinearGradientBrush();
                gradientBrush.StartPoint = new Point(0, 0);
                gradientBrush.EndPoint = new Point(0, 1);
                gradientBrush.GradientStops.Add(new GradientStop(spectrumMatchingColor, 0.0));
                gradientBrush.GradientStops.Add(new GradientStop(slightlyLighterColor, 1.0));

                return gradientBrush;
            }
            else
            {
                // Light mode gradient remains unchanged
                var mediumColor = LightenColor(accentColor, 0.5f);
                var lightColor = LightenColor(accentColor, 0.7f);

                var gradientBrush = new LinearGradientBrush();
                gradientBrush.StartPoint = new Point(0, 0);
                gradientBrush.EndPoint = new Point(0, 1);
                gradientBrush.GradientStops.Add(new GradientStop(mediumColor, 0.0));
                gradientBrush.GradientStops.Add(new GradientStop(lightColor, 1.0));

                return gradientBrush;
            }
        }

        /// <summary>
        /// Create theme-aware application title solid brush based on color scheme and theme
        /// </summary>
        private Brush CreateApplicationTitleSolidBrush(Color accentColor, bool isDark)
        {
            if (isDark)
            {
                // Dark theme: Use a lightened version of accent color for good contrast against dark menu bar
                var titleColor = LightenColor(accentColor, 0.3f);
                // Ensure minimum brightness for readability
                var hsl = RgbToHsl(titleColor);
                if (hsl.L < 0.7f) hsl.L = 0.7f;
                return new SolidColorBrush(HslToRgb(hsl));
            }
            else
            {
                // Light theme: Use a darkened version of accent color for good contrast against light menu bar
                var titleColor = DarkenColor(accentColor, 0.2f);
                // Ensure maximum brightness for readability
                var hsl = RgbToHsl(titleColor);
                if (hsl.L > 0.4f) hsl.L = 0.4f;
                return new SolidColorBrush(HslToRgb(hsl));
            }
        }

        /// <summary>
        /// Enhance color for dark theme - make it bright and luminous
        /// </summary>
        private Color EnhanceColorForDarkTheme(Color baseColor)
        {
            // Increase brightness and saturation for dark themes
            var hsl = RgbToHsl(baseColor);
            hsl.L = Math.Max(hsl.L, 0.7f); // Ensure high lightness
            hsl.S = Math.Max(hsl.S, 0.8f); // Ensure high saturation
            return HslToRgb(hsl);
        }

        /// <summary>
        /// Enhance color for light theme - make it saturated but not too bright
        /// </summary>
        private Color EnhanceColorForLightTheme(Color baseColor)
        {
            // Increase saturation but keep moderate lightness for light themes
            var hsl = RgbToHsl(baseColor);
            hsl.L = Math.Min(Math.Max(hsl.L, 0.3f), 0.6f); // Moderate lightness
            hsl.S = Math.Max(hsl.S, 0.9f); // High saturation
            return HslToRgb(hsl);
        }

        /// <summary>
        /// Get a vibrant complementary color for the gradient
        /// </summary>
        private Color GetVibrantComplementaryColor(Color baseColor, bool isDark)
        {
            var hsl = RgbToHsl(baseColor);

            // Shift hue by 120-180 degrees for complementary effect
            hsl.H = (hsl.H + 150) % 360;

            if (isDark)
            {
                // For dark theme: bright complementary color
                hsl.L = Math.Max(hsl.L, 0.6f);
                hsl.S = Math.Max(hsl.S, 0.7f);
            }
            else
            {
                // For light theme: darker complementary color
                hsl.L = Math.Min(Math.Max(hsl.L, 0.2f), 0.5f);
                hsl.S = Math.Max(hsl.S, 0.8f);
            }

            return HslToRgb(hsl);
        }

        /// <summary>
        /// Convert RGB to HSL color space
        /// </summary>
        private (float H, float S, float L) RgbToHsl(Color rgb)
        {
            float r = rgb.R / 255f;
            float g = rgb.G / 255f;
            float b = rgb.B / 255f;

            float max = Math.Max(r, Math.Max(g, b));
            float min = Math.Min(r, Math.Min(g, b));
            float diff = max - min;

            float h = 0, s = 0, l = (max + min) / 2;

            if (diff != 0)
            {
                s = l > 0.5f ? diff / (2 - max - min) : diff / (max + min);

                if (max == r)
                    h = (g - b) / diff + (g < b ? 6 : 0);
                else if (max == g)
                    h = (b - r) / diff + 2;
                else if (max == b)
                    h = (r - g) / diff + 4;

                h /= 6;
            }

            return (h * 360, s, l);
        }

        /// <summary>
        /// Convert HSL to RGB color space
        /// </summary>
        private Color HslToRgb((float H, float S, float L) hsl)
        {
            float h = hsl.H / 360f;
            float s = hsl.S;
            float l = hsl.L;

            float r, g, b;

            if (s == 0)
            {
                r = g = b = l; // achromatic
            }
            else
            {
                float q = l < 0.5f ? l * (1 + s) : l + s - l * s;
                float p = 2 * l - q;
                r = HueToRgb(p, q, h + 1f / 3f);
                g = HueToRgb(p, q, h);
                b = HueToRgb(p, q, h - 1f / 3f);
            }

            return Color.FromRgb(
                (byte)Math.Round(r * 255),
                (byte)Math.Round(g * 255),
                (byte)Math.Round(b * 255)
            );
        }

        /// <summary>
        /// Helper method for HSL to RGB conversion
        /// </summary>
        private float HueToRgb(float p, float q, float t)
        {
            if (t < 0f) t += 1f;
            if (t > 1f) t -= 1f;
            if (t < 1f / 6f) return p + (q - p) * 6f * t;
            if (t < 1f / 2f) return q;
            if (t < 2f / 3f) return p + (q - p) * (2f / 3f - t) * 6f;
            return p;
        }

        /// <summary>
        /// Create color scheme selector background brush - Adjusted for optimal visibility
        /// </summary>
        private Brush CreateColorSchemeSelectorBrush(Color accentColor, bool isDark)
        {
            if (isDark)
            {
                // Slightly darker than theme toggle for better visual hierarchy
                var lightColor = DarkenColor(accentColor, 0.15f); // Slightly darker than accent color
                var lighterColor = DarkenColor(accentColor, 0.05f); // Less dark for gradient

                var gradientBrush = new LinearGradientBrush();
                gradientBrush.StartPoint = new Point(0, 0);
                gradientBrush.EndPoint = new Point(0, 1);
                gradientBrush.GradientStops.Add(new GradientStop(lightColor, 0.0));
                gradientBrush.GradientStops.Add(new GradientStop(lighterColor, 1.0));

                return gradientBrush;
            }
            else
            {
                // Light mode - darker for better visibility
                var mediumColor = LightenColor(accentColor, 0.5f);
                var lightColor = LightenColor(accentColor, 0.65f);

                var gradientBrush = new LinearGradientBrush();
                gradientBrush.StartPoint = new Point(0, 0);
                gradientBrush.EndPoint = new Point(0, 1);
                gradientBrush.GradientStops.Add(new GradientStop(mediumColor, 0.0));
                gradientBrush.GradientStops.Add(new GradientStop(lightColor, 1.0));

                return gradientBrush;
            }
        }

        /// <summary>
        /// Create selected row brush based on color scheme and theme - Enhanced for better emphasis, deeper than banded rows
        /// </summary>
        private Brush CreateSelectedRowBrush(Color accentColor, bool isDark)
        {
            if (isDark)
            {
                // Dark theme: Use lighter version but more opaque than alternating rows for better selection visibility
                var selectedColor = LightenColor(accentColor, 0.25f); // More lightening than alternating rows (0.15f)
                selectedColor.A = 120; // More opaque than alternating rows (40) for better emphasis
                return new SolidColorBrush(selectedColor);
            }
            else
            {
                // Light theme: Use darker version than alternating rows for better selection visibility
                var selectedColor = LightenColor(accentColor, 0.65f); // Less lightening than alternating rows (0.8f)
                selectedColor.A = 180; // More opaque than alternating rows (140) for better emphasis
                return new SolidColorBrush(selectedColor);
            }
        }

        /// <summary>
        /// Create hover brush based on color scheme and theme - For dropdown menu hover effects
        /// </summary>
        private Brush CreateHoverBrush(Color accentColor, bool isDark)
        {
            if (isDark)
            {
                // Dark theme: Use lighter version for hover effect
                var hoverColor = LightenColor(accentColor, 0.2f);
                hoverColor.A = 180; // Semi-transparent for subtle hover effect
                return new SolidColorBrush(hoverColor);
            }
            else
            {
                // Light theme: Use slightly darker version for hover effect
                var hoverColor = LightenColor(accentColor, 0.8f);
                hoverColor.A = 200; // Semi-transparent for subtle hover effect
                return new SolidColorBrush(hoverColor);
            }
        }

        /// <summary>
        /// Get selected row color for resource updates - Enhanced for better emphasis, deeper than banded rows
        /// </summary>
        private Color GetSelectedRowColor(Color accentColor, bool isDark)
        {
            if (isDark)
            {
                // Dark theme: Use lighter version but more opaque than alternating rows
                var selectedColor = LightenColor(accentColor, 0.25f); // More lightening than alternating rows (0.15f)
                selectedColor.A = 120; // More opaque than alternating rows (40)
                return selectedColor;
            }
            else
            {
                // Light theme: Use darker version than alternating rows
                var selectedColor = LightenColor(accentColor, 0.65f); // Less lightening than alternating rows (0.8f)
                selectedColor.A = 180; // More opaque than alternating rows (140)
                return selectedColor;
            }
        }

        /// <summary>
        /// Create menu background brush based on color scheme and theme - VISUAL HIERARCHY ENHANCEMENT
        /// </summary>
        private Brush CreateButtonBackgroundBrush(Color accentColor, bool isDark)
        {
            return CreateMenuBackgroundBrush(accentColor, isDark);
        }

        /// <summary>
        /// Get button background color synchronized with menu bar
        /// </summary>
        private Color GetButtonBackgroundColor(Color accentColor, bool isDark)
        {
            return GetMenuBackgroundColor(accentColor, isDark);
        }

        /// <summary>
        /// Create alternating row brush based on color scheme and theme - HARMONIZED WITH SPECTRUM COLORS
        /// </summary>
        private Brush CreateAlternatingRowBrush(Color accentColor, bool isDark)
        {
            if (isDark)
            {
                // HARMONIZED: Dark theme - Use more subtle version with higher transparency
                // Reduced lightening and increased transparency to make it less obvious
                var alternatingColor = LightenColor(accentColor, 0.15f);
                alternatingColor.A = 40; // Higher transparency for more subtle effect
                return new SolidColorBrush(alternatingColor);
            }
            else
            {
                // Light theme: Use darker version of accent color for better visibility
                var alternatingColor = LightenColor(accentColor, 0.8f); // Less lightening for more visibility
                alternatingColor.A = 140; // Slightly more opaque for better contrast
                return new SolidColorBrush(alternatingColor);
            }
        }

        /// <summary>
        /// Get alternating row color for resource updates - HARMONIZED WITH SPECTRUM COLORS
        /// </summary>
        private Color GetAlternatingRowColor(Color accentColor, bool isDark)
        {
            if (isDark)
            {
                // HARMONIZED: Dark theme - Use more subtle version with higher transparency
                var alternatingColor = LightenColor(accentColor, 0.15f);
                alternatingColor.A = 40; // Higher transparency for more subtle effect
                return alternatingColor;
            }
            else
            {
                // Light theme: Use darker version for better visibility
                var alternatingColor = LightenColor(accentColor, 0.8f); // Less lightening for more visibility
                alternatingColor.A = 140; // Slightly more opaque for better contrast
                return alternatingColor;
            }
        }

        /// <summary>
        /// Force refresh of all color-dependent properties to ensure UI updates
        /// </summary>
        private void RefreshColorDependentProperties()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== STARTING COLOR REFRESH FOR {_colorScheme} ===");

                // Clear any cached brushes to force recreation with new colors
                _chartDataBrush = null;
                _chartSpectrumBrush = null;

                // Trigger refresh of chart colors by accessing the properties
                var _ = ChartDataBrush;
                var __ = ChartSpectrumBrush;

                // Force update of all theme resources
                Application.Current?.Dispatcher.Invoke(() =>
                {
                    try
                    {
                        var newAccentColor = GetAccentColor(_colorScheme);
                        var newAccentBrush = GetAccentBrush(IsDarkTheme);

                        System.Diagnostics.Debug.WriteLine($"New accent color: {newAccentColor}");
                        System.Diagnostics.Debug.WriteLine($"Is dark theme: {IsDarkTheme}");

                        // Create theme-specific brushes based on color scheme
                        var headerGradientBrush = CreateTableHeaderGradientBrush(newAccentColor, IsDarkTheme);
                        var selectedRowBrush = CreateSelectedRowBrush(newAccentColor, IsDarkTheme);
                        var hoverBrush = CreateHoverBrush(newAccentColor, IsDarkTheme);
                        var alternatingRowBrush = CreateAlternatingRowBrush(newAccentColor, IsDarkTheme);
                        var menuBackgroundBrush = CreateMenuBackgroundBrush(newAccentColor, IsDarkTheme);
                        var buttonBackgroundBrush = CreateButtonBackgroundBrush(newAccentColor, IsDarkTheme);
                        var colorSchemeSelectorBrush = CreateColorSchemeSelectorBrush(newAccentColor, IsDarkTheme);
                        var applicationTitleSolidBrush = CreateApplicationTitleSolidBrush(newAccentColor, IsDarkTheme);

                        // Update all accent-related resources including table styling and menu bar
                        var resourcesToUpdate = new Dictionary<string, object>
                        {
                            ["PrimaryColor"] = newAccentColor,
                            ["PrimaryBrush"] = newAccentBrush,
                            ["AccentColor"] = newAccentColor,
                            ["AccentBrush"] = newAccentBrush,
                            ["FocusBorderColor"] = newAccentColor,
                            ["FocusBorderBrush"] = newAccentBrush,
                            // Table header and selection colors
                            ["DataGridHeaderGradientBrush"] = headerGradientBrush,
                            ["SelectedBrush"] = selectedRowBrush,
                            ["SelectedRowBrush"] = selectedRowBrush,
                            ["SelectedColor"] = GetSelectedRowColor(newAccentColor, IsDarkTheme),
                            // Hover colors
                            ["HoverBrush"] = hoverBrush,
                            // Alternating row colors
                            ["AlternatingRowBrush"] = alternatingRowBrush,
                            ["AlternatingRowColor"] = GetAlternatingRowColor(newAccentColor, IsDarkTheme),
                            // Menu bar colors
                            ["MenuBackgroundBrush"] = menuBackgroundBrush,
                            ["MenuBackgroundColor"] = GetMenuBackgroundColor(newAccentColor, IsDarkTheme),
                            // Button colors
                            ["ButtonBackgroundBrush"] = buttonBackgroundBrush,
                            ["ButtonBackgroundColor"] = GetButtonBackgroundColor(newAccentColor, IsDarkTheme),
                            // Color scheme selector colors
                            ["ColorSchemeSelectorBrush"] = colorSchemeSelectorBrush,
                            // Application title solid color
                            ["ApplicationTitleSolidBrush"] = applicationTitleSolidBrush
                        };

                        foreach (var resource in resourcesToUpdate)
                        {
                            if (Application.Current.Resources.Contains(resource.Key))
                            {
                                Application.Current.Resources[resource.Key] = resource.Value;
                                System.Diagnostics.Debug.WriteLine($"Updated resource: {resource.Key}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"Resource not found: {resource.Key}");
                            }
                        }

                        // Force UI refresh by invalidating visual tree
                        if (Application.Current.MainWindow != null)
                        {
                            Application.Current.MainWindow.InvalidateVisual();
                            System.Diagnostics.Debug.WriteLine("Invalidated main window visual");
                        }

                        System.Diagnostics.Debug.WriteLine($"=== COMPLETED COLOR REFRESH FOR {_colorScheme} ===");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"CRITICAL ERROR updating theme resources: {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                    }
                });

                System.Diagnostics.Debug.WriteLine($"Refreshed color-dependent properties for scheme: {_colorScheme}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CRITICAL ERROR refreshing color properties: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        // Cached brushes to ensure proper refresh
        private Brush _chartDataBrush;
        private Brush _chartSpectrumBrush;

        // Get theme-aware resource
        public T GetThemeResource<T>(string lightKey, string darkKey) where T : class
        {
            try
            {
                var key = IsDarkTheme ? darkKey : lightKey;
                return Application.Current?.FindResource(key) as T;
            }
            catch
            {
                return null;
            }
        }

        // Get theme-aware brush from resource
        public Brush GetThemeBrush(string resourceKey)
        {
            try
            {
                return Application.Current?.FindResource(resourceKey) as Brush ?? ChartTextBrush;
            }
            catch
            {
                return ChartTextBrush;
            }
        }
    }

    // Utility class for color operations
    public static class Utility
    {
        public static Brush CombineAlphaAndColor(double alpha, Brush brush)
        {
            if (brush is SolidColorBrush solidBrush)
            {
                var color = solidBrush.Color;
                var newColor = Color.FromArgb((byte)(alpha * 255), color.R, color.G, color.B);
                return new SolidColorBrush(newColor);
            }
            return brush;
        }
    }
}
