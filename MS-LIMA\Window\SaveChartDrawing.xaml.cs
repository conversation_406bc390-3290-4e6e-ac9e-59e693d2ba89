﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using ChartDrawing;
using Metabolomics.MsLima.ViewModel;

namespace Metabolomics.MsLima
{
    /// <summary>
    /// SaveChartDrawing.xaml の相互作用ロジック
    /// </summary>
    public partial class SaveChartDrawing : Window
    {
        public SaveChartDrawing(DrawVisual dv)
        {
            InitializeComponent();
            this.DataContext = new SaveChartDrawingVM(dv);
        }
    }
}
