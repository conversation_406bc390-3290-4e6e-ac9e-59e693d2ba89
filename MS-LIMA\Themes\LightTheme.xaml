<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Light Theme Color Palette -->
    <Color x:Key="PrimaryColor">#2196F3</Color>
    <Color x:Key="PrimaryDarkColor">#42A5F5</Color>
    <Color x:Key="PrimaryLightColor">#BBDEFB</Color>
    <Color x:Key="AccentColor">#FF4081</Color>

    <!-- Background Colors with proper visual hierarchy -->
    <Color x:Key="WindowBackgroundColor">#F5F5F5</Color>
    <Color x:Key="SurfaceColor">#FFFFFF</Color>
    <Color x:Key="CardColor">#FFFFFF</Color>
    <Color x:Key="MenuBackgroundColor">#E8E8E8</Color>

    <!-- Accent Colors with subtle grey tones -->
    <Color x:Key="LightAccentColor">#E8E8E8</Color>
    <Color x:Key="LightAccentLightColor">#F0F0F0</Color>
    <Color x:Key="DataGridHeaderColor">#E8E8E8</Color>
    <Color x:Key="SectionLabelColor">#E8E8E8</Color>

    <!-- Text Colors -->
    <Color x:Key="PrimaryTextColor">#212121</Color>
    <Color x:Key="SecondaryTextColor">#757575</Color>
    <Color x:Key="DisabledTextColor">#BDBDBD</Color>
    <Color x:Key="HintTextColor">#9E9E9E</Color>

    <!-- Border and Divider Colors -->
    <Color x:Key="DividerColor">#E0E0E0</Color>
    <Color x:Key="BorderColor">#D0D0D0</Color>
    <Color x:Key="FocusBorderColor">#2196F3</Color>

    <!-- State Colors -->
    <Color x:Key="HoverColor">#F0F0F0</Color>
    <Color x:Key="PressedColor">#E8E8E8</Color>
    <Color x:Key="SelectedColor">#E0E0E0</Color>

    <!-- Alternating Row Colors -->
    <Color x:Key="AlternatingRowColor">#F8F8F8</Color>
    <Color x:Key="ErrorColor">#F44336</Color>
    <Color x:Key="WarningColor">#FF9800</Color>
    <Color x:Key="SuccessColor">#4CAF50</Color>

    <!-- Convert Colors to Brushes -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>

    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="{StaticResource WindowBackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="CardBrush" Color="{StaticResource CardColor}"/>
    <SolidColorBrush x:Key="MenuBackgroundBrush" Color="{StaticResource MenuBackgroundColor}"/>

    <!-- Light Blue Accent Brushes -->
    <SolidColorBrush x:Key="LightBlueAccentBrush" Color="{StaticResource LightBlueAccentColor}"/>
    <SolidColorBrush x:Key="LightBlueAccentLightBrush" Color="{StaticResource LightBlueAccentLightColor}"/>
    <SolidColorBrush x:Key="DataGridHeaderBrush" Color="{StaticResource DataGridHeaderColor}"/>
    <SolidColorBrush x:Key="SectionLabelBrush" Color="{StaticResource SectionLabelColor}"/>

    <!-- Gradient Brushes for Enhanced Visual Appeal -->
    <LinearGradientBrush x:Key="DataGridHeaderGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#E8E8E8" Offset="0"/>
        <GradientStop Color="#F0F0F0" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="SectionLabelGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#E8E8E8" Offset="0"/>
        <GradientStop Color="#F0F0F0" Offset="1"/>
    </LinearGradientBrush>

    <!-- Color Scheme Selector Brush (Same as table headers in light theme) -->
    <LinearGradientBrush x:Key="ColorSchemeSelectorBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#E8E8E8" Offset="0"/>
        <GradientStop Color="#F0F0F0" Offset="1"/>
    </LinearGradientBrush>

    <!-- Theme-aware Application Title Gradient Brush -->
    <LinearGradientBrush x:Key="ApplicationTitleGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="#1976D2" Offset="0"/>
        <GradientStop Color="#C2185B" Offset="1"/>
    </LinearGradientBrush>

    <SolidColorBrush x:Key="PrimaryTextBrush" Color="{StaticResource PrimaryTextColor}"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="{StaticResource SecondaryTextColor}"/>
    <SolidColorBrush x:Key="DisabledTextBrush" Color="{StaticResource DisabledTextColor}"/>
    <SolidColorBrush x:Key="HintTextBrush" Color="{StaticResource HintTextColor}"/>

    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="FocusBorderBrush" Color="{StaticResource FocusBorderColor}"/>

    <!-- Theme-aware brushes - will be updated dynamically by ThemeService -->
    <SolidColorBrush x:Key="HoverBrush" Color="{StaticResource HoverColor}"/>
    <SolidColorBrush x:Key="PressedBrush" Color="{StaticResource PressedColor}"/>
    <SolidColorBrush x:Key="SelectedBrush" Color="{StaticResource SelectedColor}"/>
    <SolidColorBrush x:Key="SelectedRowBrush" Color="{StaticResource SelectedColor}"/>

    <!-- Alternating Row Brush for Banded Tables -->
    <SolidColorBrush x:Key="AlternatingRowBrush" Color="{StaticResource AlternatingRowColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
<!-- Grey background for combo text -->
    <SolidColorBrush x:Key="ComboTextBackgroundBrush" Color="#F0F0F0"/>

    <!-- Typography -->
    <FontFamily x:Key="PrimaryFontFamily">Segoe UI</FontFamily>
    <FontFamily x:Key="MonospaceFontFamily">Consolas</FontFamily>

    <!-- Font Sizes -->
    <sys:Double x:Key="HeaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    <sys:Double x:Key="SubheaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="BodyFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
    <sys:Double x:Key="CaptionFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>
    <sys:Double x:Key="SmallFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">10</sys:Double>

    <!-- Spacing and Sizing -->
    <Thickness x:Key="DefaultMargin">8</Thickness>
    <Thickness x:Key="SmallMargin">4</Thickness>
    <Thickness x:Key="LargeMargin">16</Thickness>
    <Thickness x:Key="DefaultPadding">12,8</Thickness>
    <Thickness x:Key="SmallPadding">8,4</Thickness>
    <Thickness x:Key="LargePadding">16,12</Thickness>

    <CornerRadius x:Key="DefaultCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="SmallCornerRadius">2</CornerRadius>
    <CornerRadius x:Key="LargeCornerRadius">8</CornerRadius>

    <!-- Shadows -->
    <DropShadowEffect x:Key="DefaultShadow" Color="Black" Opacity="0.1" BlurRadius="8" ShadowDepth="2" Direction="270"/>
    <DropShadowEffect x:Key="ElevatedShadow" Color="Black" Opacity="0.15" BlurRadius="12" ShadowDepth="4" Direction="270"/>

</ResourceDictionary>
