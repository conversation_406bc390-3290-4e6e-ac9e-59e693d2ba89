﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text;
using Metabolomics.Core;
using Metabolomics.Core.Utility;
using MessagePack;

namespace Metabolomics.MsLima.Bean
{
    [MessagePackObject]
    public class MassSpectrum : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        // Flag to suppress property change notifications during bulk loading
        private bool _suppressPropertyChangeNotifications = false;

        protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            if (!_suppressPropertyChangeNotifications)
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        // Method to enable/disable property change notifications for performance
        public void SetPropertyChangeNotificationMode(bool enabled)
        {
            _suppressPropertyChangeNotifications = !enabled;
        }

        private int _id;
        [Key(0)]
        public int Id {
            get => _id;
            set { _id = value; OnPropertyChanged(); }
        }

        private int _binId;
        [Key(1)]
        public int BinId {
            get => _binId;
            set { _binId = value; OnPropertyChanged(); }
        }

        private string _compoundClass;
        [Key(2)]
        public string CompoundClass {
            get => _compoundClass;
            set { _compoundClass = value; OnPropertyChanged(); }
        }

        private string _name;
        [Key(3)]
        public string Name {
            get => _name;
            set { _name = value; OnPropertyChanged(); }
        }

        private double _precursorMz;
        [Key(4)]
        public double PrecursorMz {
            get => _precursorMz;
            set { _precursorMz = value; OnPropertyChanged(); }
        }

        private float _retentionTime;
        [Key(5)]
        public float RetentionTime {
            get => _retentionTime;
            set { _retentionTime = value; OnPropertyChanged(); }
        }

        private float _retentionIndex;
        [Key(6)]
        public float RetentionIndex {
            get => _retentionIndex;
            set { _retentionIndex = value; OnPropertyChanged(); }
        }

        private string _formula;
        [Key(7)]
        public string Formula {
            get => _formula;
            set { _formula = value; OnPropertyChanged(); }
        }

        private IonMode _ionMode;
        [Key(8)]
        public IonMode IonMode {
            get => _ionMode;
            set { _ionMode = value; OnPropertyChanged(); }
        }

        private string _smiles;
        [Key(9)]
        public string Smiles {
            get => _smiles;
            set { _smiles = value; OnPropertyChanged(); }
        }

        private string _inChIKey;
        [Key(10)]
        public string InChIKey {
            get => _inChIKey;
            set { _inChIKey = value; OnPropertyChanged(); }
        }

        private string _shortInChIKey;
        [Key(11)]
        public string ShortInChIKey {
            get => _shortInChIKey;
            set { _shortInChIKey = value; OnPropertyChanged(); }
        }

        private string _inChI;
        [Key(12)]
        public string InChI {
            get => _inChI;
            set { _inChI = value; OnPropertyChanged(); }
        }

        private bool _target;
        [Key(13)]
        public bool Target {
            get => _target;
            set { _target = value; OnPropertyChanged(); }
        }

        private string _authors;
        [Key(14)]
        public string Authors {
            get => _authors;
            set { _authors = value; OnPropertyChanged(); }
        }

        private string _spectrumType;
        [Key(15)]
        public string SpectrumType {
            get => _spectrumType;
            set { _spectrumType = value; OnPropertyChanged(); }
        }

        private string _instrument;
        [Key(16)]
        public string Instrument {
            get => _instrument;
            set { _instrument = value; OnPropertyChanged(); }
        }

        private string _instrumentType;
        [Key(17)]
        public string InstrumentType {
            get => _instrumentType;
            set { _instrumentType = value; OnPropertyChanged(); }
        }

        private string _license;
        [Key(18)]
        public string License {
            get => _license;
            set { _license = value; OnPropertyChanged(); }
        }

        private string _links;
        [Key(19)]
        public string Links {
            get => _links;
            set { _links = value; OnPropertyChanged(); }
        }

        private AdductIon _adductIon;
        [Key(20)]
        public AdductIon AdductIon {
            get => _adductIon;
            set { _adductIon = value; OnPropertyChanged(); }
        }

        private string _comment;
        [Key(21)]
        public string Comment {
            get => _comment;
            set { _comment = value; OnPropertyChanged(); }
        }

        private float _collisionEnergy;
        [Key(22)]
        public float CollisionEnergy {
            get => _collisionEnergy;
            set { _collisionEnergy = value; OnPropertyChanged(); }
        }

        private string _ontology;
        [Key(23)]
        public string Ontology {
            get => _ontology;
            set { _ontology = value; OnPropertyChanged(); }
        }

        private float _intensity;
        [Key(24)]
        public float Intensity {
            get => _intensity;
            set { _intensity = value; OnPropertyChanged(); }
        }

        private double _theoreticalMass;
        [Key(25)]
        public double TheoreticalMass {
            get => _theoreticalMass;
            set { _theoreticalMass = value; OnPropertyChanged(); }
        }

        [Key(26)]
        public float DiffPpm { get => (float)CommonUtility.PpmCalculator(this.TheoreticalMass, this.PrecursorMz); }

        private int _order;
        [Key(27)]
        public int Order {
            get => _order;
            set { _order = value; OnPropertyChanged(); }
        }

        private List<string> _otherMetaData = new List<string>();
        [Key(28)]
        public List<string> OtherMetaData {
            get => _otherMetaData;
            set { _otherMetaData = value; OnPropertyChanged(); }
        }

        [Key(29)]
        public int PeakNumber { get => Spectrum.Count; }

        private List<AnnotatedPeak> _spectrum;
        [Key(30)]
        public List<AnnotatedPeak> Spectrum {
            get => _spectrum;
            set { _spectrum = value; OnPropertyChanged(); }
        }

        private string _msLevel;
        [Key(31)]
        public string MsLevel {
            get => _msLevel;
            set { _msLevel = value; OnPropertyChanged(); }
        }

        private string _iupacName;
        [Key(32)]
        public string IUPACname {
            get => _iupacName;
            set { _iupacName = value; OnPropertyChanged(); }
        }

        private string _cid;
        [Key(33)]
        public string CID {
            get => _cid;
            set { _cid = value; OnPropertyChanged(); }
        }

        private string _cas;
        [Key(34)]
        public string CAS {
            get => _cas;
            set { _cas = value; OnPropertyChanged(); }
        }

        private double _exactmass;
        [Key(35)]
        public double Exactmass {
            get => _exactmass;
            set { _exactmass = value; OnPropertyChanged(); }
        }

        private double _mw;
        [Key(36)]
        public double MW {
            get => _mw;
            set { _mw = value; OnPropertyChanged(); }
        }

        private double _xLogP;
        [Key(37)]
        public double XLogP {
            get => _xLogP;
            set { _xLogP = value; OnPropertyChanged(); }
        }

        private double _mLogP;
        [Key(38)]
        public double MLogP {
            get => _mLogP;
            set { _mLogP = value; OnPropertyChanged(); }
        }

        private double _aLogP;
        [Key(39)]
        public double ALogP {
            get => _aLogP;
            set { _aLogP = value; OnPropertyChanged(); }
        }

        private int _numBond;
        [Key(40)]
        public int NumBond {
            get => _numBond;
            set { _numBond = value; OnPropertyChanged(); }
        }

        private int _numAtom;
        [Key(41)]
        public int NumAtom {
            get => _numAtom;
            set { _numAtom = value; OnPropertyChanged(); }
        }

        private string _superclass;
        [Key(42)]
        public string Superclass {
            get => _superclass;
            set { _superclass = value; OnPropertyChanged(); }
        }

        private string _class;
        [Key(43)]
        public string Class {
            get => _class;
            set { _class = value; OnPropertyChanged(); }
        }

        private string _subclass;
        [Key(45)]
        public string Subclass {
            get => _subclass;
            set { _subclass = value; OnPropertyChanged(); }
        }

        private string _cramerrules;
        [Key(46)]
        public string Cramerrules {
            get => _cramerrules;
            set { _cramerrules = value; OnPropertyChanged(); }
        }


        private string _svhc;
        [Key(44)]
        public string SVHC {
            get => _svhc;
            set { _svhc = value; OnPropertyChanged(); }
        }

        private string _cmr;
        [Key(47)]
        public string CMR {
            get => _cmr;
            set { _cmr = value; OnPropertyChanged(); }
        }

        private string _cmrSuspect;
        [Key(48)]
        public string CMRsuspect {
            get => _cmrSuspect;
            set { _cmrSuspect = value; OnPropertyChanged(); }
        }

        private string _edc;
        [Key(49)]
        public string EDC {
            get => _edc;
            set { _edc = value; OnPropertyChanged(); }
        }

        private string _iarc;
        [Key(50)]
        public string IARC {
            get => _iarc;
            set { _iarc = value; OnPropertyChanged(); }
        }

        private string _eusml;
        [Key(51)]
        public string Eusml {
            get => _eusml;
            set { _eusml = value; OnPropertyChanged(); }
        }

        private string _chinaSml;
        [Key(52)]
        public string ChinaSml {
            get => _chinaSml;
            set { _chinaSml = value; OnPropertyChanged(); }
        }

        private double _topoPSA;
        [Key(53)]
        public double TopoPSA {
            get => _topoPSA;
            set { _topoPSA = value; OnPropertyChanged(); }
        }

        // Additional toxicological properties for comprehensive MSP support
        private string _carcinogenicity_ISS;
        [Key(54)]
        public string Carcinogenicity_ISS {
            get => _carcinogenicity_ISS;
            set { _carcinogenicity_ISS = value; OnPropertyChanged(); }
        }

        private string _dna_alerts_OASIS;
        [Key(55)]
        public string DNA_alerts_OASIS {
            get => _dna_alerts_OASIS;
            set { _dna_alerts_OASIS = value; OnPropertyChanged(); }
        }

        private string _dna_binding_OASIS;
        [Key(56)]
        public string DNA_binding_OASIS {
            get => _dna_binding_OASIS;
            set { _dna_binding_OASIS = value; OnPropertyChanged(); }
        }

        private string _dna_binding_OECD;
        [Key(57)]
        public string DNA_binding_OECD {
            get => _dna_binding_OECD;
            set { _dna_binding_OECD = value; OnPropertyChanged(); }
        }

        private string _protein_binding_alerts_OASIS;
        [Key(58)]
        public string Protein_binding_alerts_OASIS {
            get => _protein_binding_alerts_OASIS;
            set { _protein_binding_alerts_OASIS = value; OnPropertyChanged(); }
        }

        private string _vitro_mutagenicity_alerts_ISS;
        [Key(59)]
        public string Vitro_mutagenicity_alerts_ISS {
            get => _vitro_mutagenicity_alerts_ISS;
            set { _vitro_mutagenicity_alerts_ISS = value; OnPropertyChanged(); }
        }

        private string _vivo_mutagenicity_alerts_ISS;
        [Key(60)]
        public string Vivo_mutagenicity_alerts_ISS {
            get => _vivo_mutagenicity_alerts_ISS;
            set { _vivo_mutagenicity_alerts_ISS = value; OnPropertyChanged(); }
        }

        private string _date;
        [Key(61)]
        public string Date {
            get => _date;
            set { _date = value; OnPropertyChanged(); }
        }

        public MassSpectrum Copy()
        {
            var spectrum = new MassSpectrum()
            {
                Id = this.Id,
                BinId = this.BinId,
                CompoundClass = this.CompoundClass,
                Name = this.Name,
                PrecursorMz = this.PrecursorMz,
                RetentionTime = this.RetentionTime,
                RetentionIndex = this.RetentionIndex,
                Formula = this.Formula,
                IonMode = this.IonMode,
                Smiles = this.Smiles,
                InChIKey = this.InChIKey,
                ShortInChIKey = this.ShortInChIKey,
                InChI = this.InChI,
                Target = this.Target,
                Authors = this.Authors,
                SpectrumType = this.SpectrumType,
                Instrument = this.Instrument,
                InstrumentType = this.InstrumentType,
                License = this.License,
                Links = this.Links,
                Comment = this.Comment,
                CollisionEnergy = this.CollisionEnergy,
                Ontology = this.Ontology,
                Intensity = this.Intensity,
                TheoreticalMass = this.TheoreticalMass,
                Order = this.Order,
                IUPACname = this.IUPACname,
                CID = this.CID,
                CAS = this.CAS,
                Exactmass = this.Exactmass,
                MW = this.MW,
                XLogP = this.XLogP,
                MLogP = this.MLogP,
                ALogP = this.ALogP,
                NumBond = this.NumBond,
                NumAtom = this.NumAtom,
                Superclass = this.Superclass,
                Class = this.Class,
                SVHC = this.SVHC,
                CMR = this.CMR,
                CMRsuspect = this.CMRsuspect,
                EDC = this.EDC,
                IARC = this.IARC,
                Eusml = this.Eusml,
                ChinaSml = this.ChinaSml,
                TopoPSA = this.TopoPSA,
                Carcinogenicity_ISS = this.Carcinogenicity_ISS,
                DNA_alerts_OASIS = this.DNA_alerts_OASIS,
                DNA_binding_OASIS = this.DNA_binding_OASIS,
                DNA_binding_OECD = this.DNA_binding_OECD,
                Protein_binding_alerts_OASIS = this.Protein_binding_alerts_OASIS,
                Vitro_mutagenicity_alerts_ISS = this.Vitro_mutagenicity_alerts_ISS,
                Vivo_mutagenicity_alerts_ISS = this.Vivo_mutagenicity_alerts_ISS,
                Date = this.Date,
                Subclass = this.Subclass,
                Cramerrules = this.Cramerrules,
                Spectrum = new List<AnnotatedPeak>()
            };
            if (this.AdductIon != null)
                spectrum.AdductIon = Metabolomics.Core.Parser.AdductIonParser.GetAdductIon(this.AdductIon.AdductIonName);

            foreach (var o in this.OtherMetaData)
            {
                spectrum.OtherMetaData.Add(o);
            }

            foreach (var peak in this.Spectrum) {
                spectrum.Spectrum.Add(peak.Copy());
            }
            return spectrum;
        }
    }
}
