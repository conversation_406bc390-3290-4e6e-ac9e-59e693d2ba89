using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace Metabolomics.MsLima.Controls
{
    /// <summary>
    /// Interaction logic for ThemeToggle.xaml
    /// </summary>
    public partial class ThemeToggle : UserControl
    {
        public static readonly DependencyProperty IsDarkThemeProperty =
            DependencyProperty.Register("IsDarkTheme", typeof(bool), typeof(ThemeToggle),
                new PropertyMetadata(false, OnIsDarkThemeChanged));

        public bool IsDarkTheme
        {
            get { return (bool)GetValue(IsDarkThemeProperty); }
            set { SetValue(IsDarkThemeProperty, value); }
        }

        public event EventHandler<bool> ThemeChanged;

        public ThemeToggle()
        {
            InitializeComponent();
        }

        private static void OnIsDarkThemeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = d as ThemeToggle;
            if (control != null)
            {
                control.ThemeChanged?.Invoke(control, (bool)e.NewValue);
            }
        }

        /// <summary>
        /// FIXED theme toggle click handler - properly updates ThemeManager for complete theme application
        /// </summary>
        private async void ThemeToggleButton_Click(object sender, RoutedEventArgs e)
        {
            // Disable the button temporarily to prevent rapid clicking
            ThemeToggleButton.IsEnabled = false;

            try
            {
                var newTheme = ThemeToggleButton.IsChecked ?? false;
                System.Diagnostics.Debug.WriteLine($"FIXED Theme toggle clicked: switching to {(newTheme ? "Dark" : "Light")} theme");

                // CRITICAL FIX: Update the property immediately for responsive UI
                IsDarkTheme = newTheme;

                // CRITICAL FIX: Update ThemeManager to trigger complete theme application
                System.Diagnostics.Debug.WriteLine($"CRITICAL FIX: Updating ThemeManager to apply {(newTheme ? "Dark" : "Light")} theme");
                await Metabolomics.MsLima.Services.ThemeManager.Instance.SetThemeAsync(newTheme);

                System.Diagnostics.Debug.WriteLine($"FIXED Theme toggle completed successfully - ThemeManager updated");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CRITICAL ERROR in theme toggle: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // Reset the toggle button state on error
                ThemeToggleButton.IsChecked = !ThemeToggleButton.IsChecked;
                IsDarkTheme = ThemeToggleButton.IsChecked ?? false;
            }
            finally
            {
                // Re-enable the button after a short delay to prevent rapid clicking
                await Task.Delay(100);
                ThemeToggleButton.IsEnabled = true;
            }
        }
    }
}
