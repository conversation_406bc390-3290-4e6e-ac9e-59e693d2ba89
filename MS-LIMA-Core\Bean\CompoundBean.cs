using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using Metabolomics.Core;

namespace Metabolomics.MsLima.Bean
{
    public class CompoundBean
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string RetentionTimes { get => GetRTs(Spectra.Select(x => x.RetentionTime).ToList()); }
        public double MolecularWeight { get; set; }
        public string InChIKey { get; set; }
        public string InChI { get; set; }
        public string Formula { get; set; }
        public string Smiles { get; set; }
        public int NumSpectra { get; set; }
        public List<MassSpectrum> Spectra { get; set; } = new List<MassSpectrum>();

        // Extended properties for comprehensive MSP field display
        public string Ontology { get; set; }
        public string IUPACName { get; set; }
        public string CID { get; set; }
        public string CAS { get; set; }
        public double ExactMass { get; set; }
        public double MW { get; set; }
        public double XLogP { get; set; }
        public double MLogP { get; set; }
        public double ALogP { get; set; }
        public int NumBond { get; set; }
        public int NumAtom { get; set; }
        public string Superclass { get; set; }
        public string Class { get; set; }
        public string Subclass { get; set; }
        public string Cramerrules { get; set; }
        public string SVHC { get; set; }
        public string CMR { get; set; }
        public string CMRSuspect { get; set; }
        public string EDC { get; set; }
        public string IARC { get; set; }
        public string Eusml { get; set; }
        public string ChinaSml { get; set; }

        // Additional metadata fields for comprehensive display
        public string Authors { get; set; }
        public string Instrument { get; set; }
        public string InstrumentType { get; set; }
        public string License { get; set; }
        public string Links { get; set; }
        public string Comment { get; set; }
        public string SpectrumType { get; set; }
        public string CompoundClass { get; set; }
        public float CollisionEnergy { get; set; }
        public string IonMode { get; set; }
        public string AdductIon { get; set; }
        public float RetentionTime { get; set; }
        public float RetentionIndex { get; set; }
        public double PrecursorMz { get; set; }
        public double TheoreticalMass { get; set; }
        public float DiffPpm { get; set; }
        public string MsLevel { get; set; }

        // Additional properties for 45-column layout
        public string PrecursorType { get; set; }
        public string Carcinogenicity_ISS { get; set; }
        public string DNA_alerts_OASIS { get; set; }
        public string DNA_binding_OASIS { get; set; }
        public string DNA_binding_OECD { get; set; }
        public string Protein_binding_alerts_OASIS { get; set; }
        public string Vitro_mutagenicity_alerts_ISS { get; set; }
        public string Vivo_mutagenicity_alerts_ISS { get; set; }
        public double TopoPSA { get; set; }
        public string Date { get; set; }
        public int Num_peaks { get; set; }
        public string Spectrum { get; set; }

        public string GetRTs(List<float> rts)
        {
            var rtlist = rts.OrderBy(x => x).Distinct().ToList();
            var res = "";
            for (var i = 0; i < rtlist.Count; i++)
            {
                if (i == 0)
                    res = Math.Round(rtlist[0], 2).ToString();
                else
                    res = res + ", " + Math.Round(rtlist[i], 2);
            }
            return res;
        }

    }
}
