using System;
using System.Windows.Media;

namespace ChartDrawing.Services
{
    /// <summary>
    /// Interface for theme change notifications
    /// </summary>
    public interface INotifyThemeChanged
    {
        event EventHandler ThemeChanged;
    }

    /// <summary>
    /// Interface for providing theme-aware colors and brushes for chart components
    /// </summary>
    public interface IThemeProvider : INotifyThemeChanged
    {

        /// <summary>
        /// Gets whether the current theme is dark
        /// </summary>
        bool IsDarkTheme { get; }

        /// <summary>
        /// Gets the background brush for charts
        /// </summary>
        Brush ChartBackgroundBrush { get; }

        /// <summary>
        /// Gets the surface brush for chart areas
        /// </summary>
        Brush ChartSurfaceBrush { get; }

        /// <summary>
        /// Gets the text brush for chart labels and text
        /// </summary>
        Brush ChartTextBrush { get; }

        /// <summary>
        /// Gets the secondary text brush for chart labels
        /// </summary>
        Brush ChartSecondaryTextBrush { get; }

        /// <summary>
        /// Gets the grid brush for chart grid lines
        /// </summary>
        Brush ChartGridBrush { get; }

        /// <summary>
        /// Gets the grid pen for chart grid lines
        /// </summary>
        Pen ChartGridPen { get; }

        /// <summary>
        /// Gets the border pen for chart borders
        /// </summary>
        Pen ChartBorderPen { get; }

        /// <summary>
        /// Gets the data brush for chart data visualization
        /// </summary>
        Brush ChartDataBrush { get; }

        /// <summary>
        /// Gets the data pen for chart data visualization
        /// </summary>
        Pen ChartDataPen { get; }

        /// <summary>
        /// Gets the spectrum brush for mass spectrum bars
        /// </summary>
        Brush ChartSpectrumBrush { get; }

        /// <summary>
        /// Gets the spectrum pen for mass spectrum bars
        /// </summary>
        Pen ChartSpectrumPen { get; }

        /// <summary>
        /// Gets the thick spectrum pen for mass spectrum bars
        /// </summary>
        Pen ChartSpectrumPenThick { get; }

        /// <summary>
        /// Gets the highlight brush for chart highlights
        /// </summary>
        Brush ChartHighlightBrush { get; }

        /// <summary>
        /// Gets the highlight pen for chart highlights
        /// </summary>
        Pen ChartHighlightPen { get; }

        /// <summary>
        /// Gets the consensus pen for low frequency data
        /// </summary>
        Pen ChartConsensusPenLow { get; }

        /// <summary>
        /// Gets the consensus pen for medium frequency data
        /// </summary>
        Pen ChartConsensusPenMedium { get; }

        /// <summary>
        /// Gets the consensus pen for high frequency data
        /// </summary>
        Pen ChartConsensusPenHigh { get; }

        /// <summary>
        /// Gets the label background brush for chart labels
        /// </summary>
        Brush ChartLabelBackgroundBrush { get; }
    }

    /// <summary>
    /// Static service locator for theme provider
    /// </summary>
    public static class ThemeProviderService
    {
        private static IThemeProvider _provider;

        /// <summary>
        /// Gets the current theme provider instance
        /// </summary>
        public static IThemeProvider Current => _provider ?? DefaultThemeProvider.Instance;

        /// <summary>
        /// Sets the theme provider instance
        /// </summary>
        /// <param name="provider">The theme provider to use</param>
        public static void SetProvider(IThemeProvider provider)
        {
            _provider = provider;
        }
    }

    /// <summary>
    /// Default theme provider that provides light theme colors as fallback
    /// </summary>
    internal class DefaultThemeProvider : IThemeProvider
    {
        private static readonly Lazy<DefaultThemeProvider> _instance = new Lazy<DefaultThemeProvider>(() => new DefaultThemeProvider());
        public static DefaultThemeProvider Instance => _instance.Value;

        public event EventHandler ThemeChanged;

        public bool IsDarkTheme => false;

        public Brush ChartBackgroundBrush => Brushes.White;
        public Brush ChartSurfaceBrush => new SolidColorBrush(Color.FromRgb(0xF5, 0xF5, 0xF5));
        public Brush ChartTextBrush => Brushes.Black;
        public Brush ChartSecondaryTextBrush => new SolidColorBrush(Color.FromRgb(0x66, 0x66, 0x66));
        public Brush ChartGridBrush => new SolidColorBrush(Color.FromRgb(0xE0, 0xE0, 0xE0));
        public Pen ChartGridPen => new Pen(ChartGridBrush, 0.5);
        public Pen ChartBorderPen => new Pen(ChartGridBrush, 1.0);
        public Brush ChartDataBrush => new SolidColorBrush(Color.FromRgb(0x21, 0x96, 0xF3));
        public Pen ChartDataPen => new Pen(ChartDataBrush, 1.0);
        public Brush ChartSpectrumBrush => new SolidColorBrush(Color.FromRgb(0x21, 0x96, 0xF3));
        public Pen ChartSpectrumPen => new Pen(ChartSpectrumBrush, 1.0);
        public Pen ChartSpectrumPenThick => new Pen(ChartSpectrumBrush, 2.0);
        public Brush ChartHighlightBrush => new SolidColorBrush(Color.FromRgb(0xFF, 0x40, 0x81));
        public Pen ChartHighlightPen => new Pen(ChartHighlightBrush, 2.5);
        public Pen ChartConsensusPenLow => new Pen(Brushes.Black, 1);
        public Pen ChartConsensusPenMedium => new Pen(new SolidColorBrush(Color.FromRgb(0x21, 0x96, 0xF3)), 1);
        public Pen ChartConsensusPenHigh => new Pen(new SolidColorBrush(Color.FromRgb(0xFF, 0x40, 0x81)), 1);
        public Brush ChartLabelBackgroundBrush => CombineAlphaAndColor(0.9, Brushes.White);

        private static Brush CombineAlphaAndColor(double alpha, Brush brush)
        {
            if (brush is SolidColorBrush solidBrush)
            {
                var color = solidBrush.Color;
                var newColor = Color.FromArgb((byte)(alpha * 255), color.R, color.G, color.B);
                return new SolidColorBrush(newColor);
            }
            return brush;
        }

        private DefaultThemeProvider() { }
    }
}
