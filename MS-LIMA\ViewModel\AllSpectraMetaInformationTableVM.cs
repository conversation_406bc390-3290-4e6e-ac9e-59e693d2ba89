using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using Metabolomics.MsLima.Bean;

namespace Metabolomics.MsLima.ViewModel
{
    public class AllSpectraMetaInformationTableVM : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private List<MassSpectrum> _table = new List<MassSpectrum>();
        public List<MassSpectrum> Table
        {
            get => _table;
            set
            {
                _table = value;
                OnPropertyChanged();
            }
        }

        // Dictionary to store column filter values
        private Dictionary<string, string> _columnFilterValues = new Dictionary<string, string>();
        public Dictionary<string, string> ColumnFilterValues
        {
            get => _columnFilterValues;
            set
            {
                _columnFilterValues = value;
                OnPropertyChanged();
            }
        }

        public AllSpectraMetaInformationTableVM()
        {
            // Initialize filter values for all columns
            ColumnFilterValues = new Dictionary<string, string>
            {
                {"ID", ""},
                {"Name", ""},
                {"Retention time", ""},
                {"m/z", ""},
                {"Theor. m/z", ""},
                {"Diff (ppm)", ""},
                {"Type", ""},
                {"Collision Energy", ""},
                {"Formula", ""},
                {"InChIKey", ""},
                {"NumPeaks", ""},
                {"Comment", ""},
                {"IUPAC Name", ""},
                {"CID", ""},
                {"CAS", ""},
                {"Exact Mass", ""},
                {"MW", ""},
                {"XLogP", ""},
                {"MLogP", ""},
                {"ALogP", ""},
                {"NumBond", ""},
                {"NumAtom", ""},
                {"Superclass", ""},
                {"Class", ""},
                {"Subclass", ""},
                {"Cramer Rules", ""},
                {"SVHC", ""},
                {"CMR", ""},
                {"CMR Suspect", ""},
                {"EDC", ""},
                {"IARC", ""},
                {"EUSML", ""},
                {"ChinaSML", ""},
                {"Ontology", ""}
            };
        }

        public AllSpectraMetaInformationTableVM(MsLimaData data) : this()
        {
            foreach(var comp in data.DataStorage.CompoundList)
            {
                foreach(var spec in comp.Spectra)
                {
                    Table.Add(spec);
                }
            }
        }

        // Method to get a column filter value
        public string GetColumnFilterValue(string columnName)
        {
            if (ColumnFilterValues.ContainsKey(columnName))
                return ColumnFilterValues[columnName];
            return string.Empty;
        }

        // Method to set a column filter value
        public void SetColumnFilterValue(string columnName, string value)
        {
            if (ColumnFilterValues.ContainsKey(columnName))
            {
                ColumnFilterValues[columnName] = value;
                OnPropertyChanged($"ColumnFilterValues[{columnName}]");
            }
        }
    }
}
