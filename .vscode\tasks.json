{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/MS-LIMA Windows.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/MS-LIMA Windows.sln", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary;ForceNoAlign"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/MS-LIMA Windows.sln"], "problemMatcher": "$msCompile"}]}