<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Light Theme Color Palette -->
    <Color x:Key="PrimaryColor">#2196F3</Color>
    <Color x:Key="PrimaryDarkColor">#42A5F5</Color>
    <Color x:Key="PrimaryLightColor">#BBDEFB</Color>
    <Color x:Key="AccentColor">#FF4081</Color>

    <!-- Background Colors -->
    <Color x:Key="WindowBackgroundColor">#FAFAFA</Color>
    <Color x:Key="SurfaceColor">#FFFFFF</Color>
    <Color x:Key="CardColor">#FFFFFF</Color>
    <Color x:Key="MenuBackgroundColor">#c4dbe1</Color>

    <!-- Light Blue Accent Colors (updated to softer blue-gray) -->
    <Color x:Key="LightBlueAccentColor">#c4dbe1</Color>
    <Color x:Key="LightBlueAccentLightColor">#e8f1f3</Color>
    <Color x:Key="DataGridHeaderColor">#c4dbe1</Color>
    <Color x:Key="SectionLabelColor">#c4dbe1</Color>

    <!-- Text Colors -->
    <Color x:Key="PrimaryTextColor">#212121</Color>
    <Color x:Key="SecondaryTextColor">#757575</Color>
    <Color x:Key="DisabledTextColor">#BDBDBD</Color>
    <Color x:Key="HintTextColor">#9E9E9E</Color>

    <!-- Border and Divider Colors -->
    <Color x:Key="DividerColor">#E0E0E0</Color>
    <Color x:Key="BorderColor">#E0E0E0</Color>
    <Color x:Key="FocusBorderColor">#2196F3</Color>

    <!-- State Colors -->
    <Color x:Key="HoverColor">#F8F8F8</Color>
    <Color x:Key="PressedColor">#F0F0F0</Color>
    <Color x:Key="SelectedColor">#F0F4F8</Color>

    <!-- Alternating Row Colors for Banded Tables -->
    <Color x:Key="AlternatingRowColor">#F0F0F0</Color>
    <Color x:Key="ErrorColor">#F44336</Color>
    <Color x:Key="WarningColor">#FF9800</Color>
    <Color x:Key="SuccessColor">#4CAF50</Color>

    <!-- Convert Colors to Brushes -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>

    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="{StaticResource WindowBackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="CardBrush" Color="{StaticResource CardColor}"/>
    <SolidColorBrush x:Key="MenuBackgroundBrush" Color="{StaticResource MenuBackgroundColor}"/>

    <!-- Light Blue Accent Brushes -->
    <SolidColorBrush x:Key="LightBlueAccentBrush" Color="{StaticResource LightBlueAccentColor}"/>
    <SolidColorBrush x:Key="LightBlueAccentLightBrush" Color="{StaticResource LightBlueAccentLightColor}"/>
    <SolidColorBrush x:Key="DataGridHeaderBrush" Color="{StaticResource DataGridHeaderColor}"/>
    <SolidColorBrush x:Key="SectionLabelBrush" Color="{StaticResource SectionLabelColor}"/>

    <!-- Gradient Brushes for Enhanced Visual Appeal -->
    <LinearGradientBrush x:Key="DataGridHeaderGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource LightBlueAccentColor}" Offset="0"/>
        <GradientStop Color="{StaticResource LightBlueAccentLightColor}" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="SectionLabelGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource LightBlueAccentColor}" Offset="0"/>
        <GradientStop Color="{StaticResource LightBlueAccentLightColor}" Offset="1"/>
    </LinearGradientBrush>

    <SolidColorBrush x:Key="PrimaryTextBrush" Color="{StaticResource PrimaryTextColor}"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="{StaticResource SecondaryTextColor}"/>
    <SolidColorBrush x:Key="DisabledTextBrush" Color="{StaticResource DisabledTextColor}"/>
    <SolidColorBrush x:Key="HintTextBrush" Color="{StaticResource HintTextColor}"/>

    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="FocusBorderBrush" Color="{StaticResource FocusBorderColor}"/>

    <SolidColorBrush x:Key="HoverBrush" Color="{StaticResource HoverColor}"/>
    <SolidColorBrush x:Key="PressedBrush" Color="{StaticResource PressedColor}"/>
    <SolidColorBrush x:Key="SelectedBrush" Color="{StaticResource SelectedColor}"/>
    <!-- CRITICAL: Theme-aware selected row brush - will be updated dynamically by ThemeService -->
    <SolidColorBrush x:Key="SelectedRowBrush" Color="{StaticResource SelectedColor}"/>

    <!-- Alternating Row Brush for Banded Tables -->
    <SolidColorBrush x:Key="AlternatingRowBrush" Color="{StaticResource AlternatingRowColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>

    <!-- Typography -->
    <FontFamily x:Key="PrimaryFontFamily">Segoe UI</FontFamily>
    <FontFamily x:Key="MonospaceFontFamily">Consolas</FontFamily>

    <!-- Font Sizes -->
    <sys:Double x:Key="HeaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    <sys:Double x:Key="SubheaderFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="BodyFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
    <sys:Double x:Key="CaptionFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>
    <sys:Double x:Key="SmallFontSize" xmlns:sys="clr-namespace:System;assembly=mscorlib">10</sys:Double>

    <!-- Spacing and Sizing -->
    <Thickness x:Key="DefaultMargin">8</Thickness>
    <Thickness x:Key="SmallMargin">4</Thickness>
    <Thickness x:Key="LargeMargin">16</Thickness>
    <Thickness x:Key="DefaultPadding">12,8</Thickness>
    <Thickness x:Key="SmallPadding">8,4</Thickness>
    <Thickness x:Key="LargePadding">16,12</Thickness>

    <CornerRadius x:Key="DefaultCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="SmallCornerRadius">2</CornerRadius>
    <CornerRadius x:Key="LargeCornerRadius">8</CornerRadius>

    <!-- Shadows -->
    <DropShadowEffect x:Key="DefaultShadow" Color="Black" Opacity="0.1" BlurRadius="8" ShadowDepth="2" Direction="270"/>
    <DropShadowEffect x:Key="ElevatedShadow" Color="Black" Opacity="0.15" BlurRadius="12" ShadowDepth="4" Direction="270"/>

</ResourceDictionary>
