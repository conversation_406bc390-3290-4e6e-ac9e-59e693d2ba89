<UserControl x:Class="Metabolomics.MsLima.Controls.ThemeToggle"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="28" d:DesignWidth="60">

    <UserControl.Resources>
        <!-- Theme Toggle Button Style - Synchronized with Table Headers -->
        <Style x:Key="ThemeToggleStyle" TargetType="ToggleButton">
            <Setter Property="Width" Value="57"/>
            <Setter Property="Height" Value="29"/>
            <Setter Property="Background" Value="{DynamicResource DataGridHeaderGradientBrush}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="14.5"
                                SnapsToDevicePixels="True">
                            <Grid>
                                <!-- Track -->
                                <Border x:Name="Track"
                                        Background="{DynamicResource BorderBrush}"
                                        CornerRadius="14.5"
                                        Opacity="0.5"/>

                                <!-- Thumb -->
                                <Border x:Name="Thumb"
                                        Width="23"
                                        Height="23"
                                        Background="White"
                                        CornerRadius="11.5"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Margin="4,0">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="4" ShadowDepth="1"/>
                                    </Border.Effect>

                                    <!-- Icon Container -->
                                    <Grid>
                                        <!-- Sun Icon (Light Mode) -->
                                        <Path x:Name="SunIcon"
                                              Data="M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M12,2L14.39,5.42C13.65,5.15 12.84,5 12,5C11.16,5 10.35,5.15 9.61,5.42L12,2M3.34,7L7.5,6.65C6.9,7.16 6.36,7.78 5.94,8.5C5.52,9.22 5.25,10 5.11,10.79L3.34,7M3.36,17L5.12,13.23C5.26,14 5.53,14.78 5.95,15.5C6.37,16.22 6.91,16.84 7.51,17.35L3.36,17M20.65,7L18.88,10.79C18.74,10 18.47,9.22 18.05,8.5C17.63,7.78 17.09,7.15 16.49,6.65L20.65,7M20.64,17L16.5,17.35C17.1,16.84 17.64,16.22 18.06,15.5C18.48,14.78 18.75,14 18.89,13.23L20.64,17M12,22L9.59,18.56C10.33,18.83 11.14,19 12,19C12.86,19 13.67,18.83 14.41,18.56L12,22Z"
                                              Fill="#FFA500"
                                              Stretch="Uniform"
                                              Width="13"
                                              Height="13"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>

                                        <!-- Moon Icon (Dark Mode) -->
                                        <Path x:Name="MoonIcon"
                                              Data="M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95Z"
                                              Fill="#4A90E2"
                                              Stretch="Uniform"
                                              Width="13"
                                              Height="13"
                                              HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              Visibility="Collapsed"/>
                                    </Grid>
                                </Border>
                            </Grid>
                        </Border>

                        <ControlTemplate.Triggers>
                            <!-- Checked State (Dark Mode) -->
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="Track" Property="Background" Value="{DynamicResource PrimaryBrush}"/>
                                <Setter TargetName="Track" Property="Opacity" Value="1"/>
                                <Setter TargetName="Thumb" Property="HorizontalAlignment" Value="Right"/>
                                <Setter TargetName="SunIcon" Property="Visibility" Value="Collapsed"/>
                                <Setter TargetName="MoonIcon" Property="Visibility" Value="Visible"/>
                            </Trigger>

                            <!-- Hover Effects -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="8" ShadowDepth="2"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
            <!-- Light Mode Label -->
            <TextBlock Text="☀"
                       FontSize="15"
                       VerticalAlignment="Center"
                       Margin="0,0,5,0"
                       Foreground="{DynamicResource SecondaryTextBrush}"/>

            <!-- Toggle Switch -->
            <ToggleButton x:Name="ThemeToggleButton"
                          Style="{StaticResource ThemeToggleStyle}"
                          IsChecked="{Binding IsDarkTheme, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          Click="ThemeToggleButton_Click"/>

            <!-- Dark Mode Label -->
            <TextBlock Text="🌙"
                       FontSize="15"
                       VerticalAlignment="Center"
                       Margin="5,0,0,0"
                       Foreground="{DynamicResource SecondaryTextBrush}"/>
        </StackPanel>
    </Grid>
</UserControl>
