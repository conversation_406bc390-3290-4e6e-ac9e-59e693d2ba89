using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Diagnostics;
using Metabolomics.MsLima.Model;

namespace Metabolomics.MsLima.Utility
{
    /// <summary>
    /// Manages dynamic column visibility for the compound table DataGrid
    /// </summary>
    public static class DynamicColumnManager
    {
        /// <summary>
        /// Updates the DataGrid columns based on the available column information
        /// Works with existing static columns by setting their visibility
        /// </summary>
        /// <param name="dataGrid">The DataGrid to update</param>
        /// <param name="availableColumns">Dictionary of available columns with visibility info</param>
        /// <param name="filterGrid">The filter grid to synchronize with</param>
        public static void UpdateDataGridColumns(DataGrid dataGrid, Dictionary<string, ColumnInfo> availableColumns, Grid filterGrid = null)
        {
            if (dataGrid == null || availableColumns == null)
            {
                Debug.WriteLine("DynamicColumnManager: DataGrid or availableColumns is null");
                return;
            }

            try
            {
                Debug.WriteLine($"DynamicColumnManager: Updating DataGrid column visibility. Available: {availableColumns.Count}, Visible: {availableColumns.Values.Count(c => c.IsVisible)}");

                // If DataGrid has no columns (static columns were removed), create them dynamically
                if (dataGrid.Columns.Count == 0)
                {
                    CreateDynamicColumns(dataGrid, availableColumns);
                }
                else
                {
                    // Work with existing static columns by setting visibility
                    UpdateExistingColumnVisibility(dataGrid, availableColumns);
                }

                Debug.WriteLine($"DynamicColumnManager: DataGrid now has {dataGrid.Columns.Count} columns, {dataGrid.Columns.Count(c => c.Visibility == Visibility.Visible)} visible");

                // Update filter grid if provided
                if (filterGrid != null)
                {
                    UpdateFilterGrid(filterGrid, availableColumns, GetColumnDefinitions());
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateDataGridColumns: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Creates dynamic columns when no static columns exist
        /// </summary>
        private static void CreateDynamicColumns(DataGrid dataGrid, Dictionary<string, ColumnInfo> availableColumns)
        {
            Debug.WriteLine("DynamicColumnManager: Creating dynamic columns");

            // Clear existing columns
            dataGrid.Columns.Clear();

            // Define column order and properties
            var columnDefinitions = GetColumnDefinitions();

            // Create columns for visible fields only
            foreach (var columnDef in columnDefinitions)
            {
                var propertyName = columnDef.PropertyName;

                if (availableColumns.ContainsKey(propertyName) && availableColumns[propertyName].IsVisible)
                {
                    var column = CreateDataGridColumn(columnDef, availableColumns[propertyName]);
                    dataGrid.Columns.Add(column);
                    Debug.WriteLine($"Added column: {propertyName} ({availableColumns[propertyName].DisplayName})");
                }
            }
        }

        /// <summary>
        /// Updates visibility of existing static columns to match available data
        /// </summary>
        private static void UpdateExistingColumnVisibility(DataGrid dataGrid, Dictionary<string, ColumnInfo> availableColumns)
        {
            Debug.WriteLine("DynamicColumnManager: Updating existing column visibility");

            var columnDefinitions = GetColumnDefinitions();
            var propertyToColumnDef = columnDefinitions.ToDictionary(cd => cd.PropertyName, cd => cd);

            foreach (var column in dataGrid.Columns)
            {
                // Try to determine the property name from the binding
                if (column is DataGridBoundColumn boundColumn && boundColumn.Binding is Binding binding)
                {
                    var propertyName = binding.Path.Path;

                    // Handle special cases for property name mapping
                    if (propertyName == "Id" && !availableColumns.ContainsKey("Id"))
                        propertyName = "ID"; // Try alternative naming

                    if (availableColumns.ContainsKey(propertyName))
                    {
                        var shouldBeVisible = availableColumns[propertyName].IsVisible;
                        column.Visibility = shouldBeVisible ? Visibility.Visible : Visibility.Collapsed;

                        // Ensure column width matches the definition
                        if (shouldBeVisible && propertyToColumnDef.ContainsKey(propertyName))
                        {
                            var expectedWidth = propertyToColumnDef[propertyName].Width;
                            if (column.Width.Value != expectedWidth)
                            {
                                column.Width = new DataGridLength(expectedWidth);
                                Debug.WriteLine($"Updated column {propertyName} width to {expectedWidth}");
                            }
                        }

                        Debug.WriteLine($"Set column {propertyName} visibility to {column.Visibility}");
                    }
                    else
                    {
                        // Hide columns that don't have data
                        column.Visibility = Visibility.Collapsed;
                        Debug.WriteLine($"Hidden column {propertyName} (no data available)");
                    }
                }
            }
        }

        /// <summary>
        /// Creates a DataGrid column based on the column definition
        /// </summary>
        private static DataGridColumn CreateDataGridColumn(ColumnDefinition columnDef, ColumnInfo columnInfo)
        {
            var column = new DataGridTextColumn
            {
                Header = columnInfo.DisplayName,
                Width = new DataGridLength(columnDef.Width),
                IsReadOnly = columnDef.IsReadOnly,
                Binding = new Binding(columnDef.PropertyName)
            };

            // Apply string format if specified
            if (!string.IsNullOrEmpty(columnDef.StringFormat))
            {
                ((Binding)column.Binding).StringFormat = columnDef.StringFormat;
            }

            // Apply converter if specified
            if (columnDef.ConverterKey != null)
            {
                // Note: In a real implementation, you'd need to resolve the converter from resources
                // For now, we'll skip this as it requires access to the application resources
            }

            return column;
        }

        /// <summary>
        /// Updates the filter grid to perfectly match the DataGrid columns using StackPanel approach
        /// This matches the successful static filter implementation pattern for perfect alignment
        /// CRITICAL: Uses actual DataGrid column order instead of predefined order for perfect alignment
        /// </summary>
        private static void UpdateFilterGrid(Grid filterGrid, Dictionary<string, ColumnInfo> availableColumns, List<ColumnDefinition> columnDefinitions)
        {
            try
            {
                Debug.WriteLine("DynamicColumnManager: Rebuilding filter grid using StackPanel approach for perfect alignment");

                if (filterGrid?.Children == null)
                {
                    Debug.WriteLine("DynamicColumnManager: Filter grid or its children collection is null");
                    return;
                }

                // Find the DataGrid to get the actual column order
                var dataGrid = FindDataGrid(filterGrid);
                if (dataGrid == null)
                {
                    Debug.WriteLine("DynamicColumnManager: Could not find DataGrid for column order reference");
                    return;
                }

                // Clear existing filter grid structure
                filterGrid.ColumnDefinitions.Clear();
                filterGrid.Children.Clear();

                // Create a StackPanel to hold the filter TextBoxes (like the original static implementation)
                var stackPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Left,
                    VerticalAlignment = VerticalAlignment.Center
                };

                // CRITICAL FIX: Use actual DataGrid column order instead of predefined columnDefinitions order
                // This ensures filters appear in the exact same sequence as visible DataGrid columns
                var columnDefLookup = columnDefinitions.ToDictionary(cd => cd.PropertyName, cd => cd);

                foreach (var dataGridColumn in dataGrid.Columns.Where(c => c.Visibility == Visibility.Visible))
                {
                    // Extract property name from DataGrid column binding
                    string propertyName = GetPropertyNameFromColumn(dataGridColumn);

                    if (!string.IsNullOrEmpty(propertyName) &&
                        columnDefLookup.ContainsKey(propertyName) &&
                        availableColumns.ContainsKey(propertyName) &&
                        availableColumns[propertyName].IsVisible)
                    {
                        var columnDef = columnDefLookup[propertyName];
                        // Create filter TextBox with exact width matching DataGrid column
                        var filterTextBox = new TextBox
                        {
                            Width = columnDef.Width, // CRITICAL: Use exact same width as DataGrid column
                            Height = 26,
                            Margin = new Thickness(0, 3, 0, 3), // FIXED: Remove left/right margins to prevent alignment offset
                            VerticalAlignment = VerticalAlignment.Center,
                            ToolTip = $"Filter by {availableColumns[propertyName].DisplayName}",
                            Tag = availableColumns[propertyName].DisplayName // Used for placeholder text
                        };

                        // Apply the PlaceholderTextBox style for placeholder text functionality
                        if (Application.Current?.MainWindow?.Resources != null)
                        {
                            var placeholderStyle = Application.Current.MainWindow.Resources["PlaceholderTextBox"] as Style;
                            if (placeholderStyle != null)
                            {
                                filterTextBox.Style = placeholderStyle;
                                Debug.WriteLine($"Applied PlaceholderTextBox style to filter for {propertyName}");
                            }
                            else
                            {
                                Debug.WriteLine($"Warning: PlaceholderTextBox style not found for {propertyName}");
                            }
                        }

                        // Set binding for the filter using the MainWindowVM indexer property
                        var binding = new Binding($"[{propertyName}]")
                        {
                            Mode = BindingMode.TwoWay,
                            UpdateSourceTrigger = UpdateSourceTrigger.PropertyChanged,
                            Source = Application.Current.MainWindow?.DataContext
                        };
                        filterTextBox.SetBinding(TextBox.TextProperty, binding);

                        Debug.WriteLine($"Set binding for filter {propertyName}: [{propertyName}] with source: {binding.Source?.GetType()?.Name}");

                        // Add the filter TextBox to the StackPanel
                        stackPanel.Children.Add(filterTextBox);

                        Debug.WriteLine($"Added filter TextBox: {propertyName} ({availableColumns[propertyName].DisplayName}) with width {columnDef.Width}");
                    }
                }

                // Add the StackPanel to the filter grid
                filterGrid.Children.Add(stackPanel);

                Debug.WriteLine($"DynamicColumnManager: Filter grid rebuilt with {stackPanel.Children.Count} filter TextBoxes using StackPanel approach");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateFilterGrid: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Gets the column definitions with their display properties
        /// </summary>
        private static List<ColumnDefinition> GetColumnDefinitions()
        {
            return new List<ColumnDefinition>
            {
                // CRITICAL: Order must EXACTLY match the XAML DataGrid column order for perfect filter alignment

                // 1. ID (matches XAML line 793)
                new ColumnDefinition { PropertyName = "Id", Width = 60, IsReadOnly = true },
                // 2. Name (matches XAML line 795)
                new ColumnDefinition { PropertyName = "Name", Width = 200, IsReadOnly = true },
                // 3. Retention time (matches XAML line 803)
                new ColumnDefinition { PropertyName = "RetentionTimes", Width = 120, IsReadOnly = false },
                // 4. Precursor MZ (matches XAML line 805)
                new ColumnDefinition { PropertyName = "PrecursorMz", Width = 110, IsReadOnly = false, StringFormat = "0.0000" },
                // 5. Precursor type (matches XAML line 807)
                new ColumnDefinition { PropertyName = "PrecursorType", Width = 120, IsReadOnly = false },
                // 6. Ion mode (matches XAML line 815)
                new ColumnDefinition { PropertyName = "IonMode", Width = 100, IsReadOnly = false },
                // 7. CAS (matches XAML line 817) - MOVED UP to match XAML order
                new ColumnDefinition { PropertyName = "CAS", Width = 120, IsReadOnly = false },
                // 8. CID (matches XAML line 825) - MOVED UP to match XAML order
                new ColumnDefinition { PropertyName = "CID", Width = 80, IsReadOnly = false },
                // 9. Formula (matches XAML line 827) - MOVED UP to match XAML order
                new ColumnDefinition { PropertyName = "Formula", Width = 120, IsReadOnly = true },
                // 10. Exact mass (matches XAML line 835) - MOVED UP to match XAML order
                new ColumnDefinition { PropertyName = "ExactMass", Width = 100, IsReadOnly = false, StringFormat = "0.0000" },
                // 11. Collision energy (matches XAML line 838) - MOVED DOWN to match XAML order
                new ColumnDefinition { PropertyName = "CollisionEnergy", Width = 120, IsReadOnly = false, StringFormat = "0.0" },
                // 12. Num peaks (matches XAML line 840) - MOVED DOWN to match XAML order
                new ColumnDefinition { PropertyName = "Num_peaks", Width = 100, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "InChIKey", Width = 300, IsReadOnly = true },
                new ColumnDefinition { PropertyName = "InChI", Width = 300, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "IUPACName", Width = 300, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Smiles", Width = 300, IsReadOnly = false },
                
                // Classification columns
                new ColumnDefinition { PropertyName = "Superclass", Width = 150, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Class", Width = 150, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Subclass", Width = 150, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Ontology", Width = 150, IsReadOnly = false },
                
                // Toxicological/Regulatory columns
                new ColumnDefinition { PropertyName = "Cramerrules", Width = 100, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "SVHC", Width = 80, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "CMR", Width = 80, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "CMRSuspect", Width = 100, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "EDC", Width = 80, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "IARC", Width = 80, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Eusml", Width = 100, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "ChinaSml", Width = 100, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Carcinogenicity_ISS", Width = 150, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "DNA_alerts_OASIS", Width = 150, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "DNA_binding_OASIS", Width = 150, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "DNA_binding_OECD", Width = 150, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Protein_binding_alerts_OASIS", Width = 200, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Vitro_mutagenicity_alerts_ISS", Width = 200, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Vivo_mutagenicity_alerts_ISS", Width = 200, IsReadOnly = false },
                
                // Physicochemical properties
                new ColumnDefinition { PropertyName = "XLogP", Width = 80, IsReadOnly = false, StringFormat = "0.00" },
                new ColumnDefinition { PropertyName = "MLogP", Width = 80, IsReadOnly = false, StringFormat = "0.00" },
                new ColumnDefinition { PropertyName = "ALogP", Width = 80, IsReadOnly = false, StringFormat = "0.00" },
                new ColumnDefinition { PropertyName = "TopoPSA", Width = 100, IsReadOnly = false, StringFormat = "0.00" },
                new ColumnDefinition { PropertyName = "NumBond", Width = 100, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "NumAtom", Width = 100, IsReadOnly = false },
                
                // Metadata columns
                new ColumnDefinition { PropertyName = "Authors", Width = 150, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Instrument", Width = 150, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "InstrumentType", Width = 120, IsReadOnly = false },
                new ColumnDefinition { PropertyName = "Date", Width = 100, IsReadOnly = false }
            };
        }

        /// <summary>
        /// Finds the DataGrid associated with the filter grid to get actual column order
        /// </summary>
        private static DataGrid FindDataGrid(Grid filterGrid)
        {
            try
            {
                // Navigate up the visual tree to find the main window
                var parent = filterGrid.Parent;
                while (parent != null && !(parent is Window))
                {
                    parent = System.Windows.LogicalTreeHelper.GetParent(parent);
                }

                if (parent is Window window)
                {
                    // Find the DataGrid_CompoundTable by name
                    return window.FindName("DataGrid_CompoundTable") as DataGrid;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error finding DataGrid: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// Extracts the property name from a DataGrid column binding
        /// </summary>
        private static string GetPropertyNameFromColumn(DataGridColumn column)
        {
            try
            {
                if (column is DataGridBoundColumn boundColumn && boundColumn.Binding is Binding binding)
                {
                    return binding.Path.Path;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error extracting property name from column: {ex.Message}");
            }
            return null;
        }
    }

    /// <summary>
    /// Defines properties for a DataGrid column
    /// </summary>
    public class ColumnDefinition
    {
        public string PropertyName { get; set; }
        public double Width { get; set; }
        public bool IsReadOnly { get; set; }
        public string StringFormat { get; set; }
        public string ConverterKey { get; set; }
    }
}
