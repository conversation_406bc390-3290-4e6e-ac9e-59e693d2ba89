﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Win32;
using System.Windows.Input;
using System.Windows;
using System.Windows.Controls;
using Metabolomics.Core;
using Metabolomics.MsLima.Bean;

namespace Metabolomics.MsLima.Model
{
    public static class ImportUtility
    {
        public static async Task ImportFileAsync(MsLimaData msLimaData, AutoRepeater exporter, IProgress<int> progress = null)
        {
            var res = MessageBox.Show("Do you want to open with auto saving?", "Ask", MessageBoxButton.YesNo);

            OpenFileDialog ofd = new OpenFileDialog
            {
                Filter = "MSP file(*.msp)|*.msp| MGF file (*.mgf)|*.mgf| Text file (*.txt)|*.txt| all files(*)|*;",
                Title = "Import a library file",
                RestoreDirectory = true,
                Multiselect = false
            };

            if (ofd.ShowDialog() == true)
            {
                Mouse.OverrideCursor = Cursors.Wait;

                if (exporter.ExportTimer.Enabled) exporter.Stop();

                try
                {
                    if (res == MessageBoxResult.Yes)
                    {
                        var dt = DateTime.Now;
                        var newFilePath = System.IO.Path.GetDirectoryName(ofd.FileName) + "\\" + System.IO.Path.GetFileNameWithoutExtension(ofd.FileName) + "_StartMod_"
                            + dt.ToString("yy_MM_dd_HH_mm_ss") + System.IO.Path.GetExtension(ofd.FileName);
                        System.IO.File.Copy(ofd.FileName, newFilePath, true);
                        await msLimaData.DataStorage.SetLibraryAsync(newFilePath, msLimaData.Parameter.CompoundGroupingKey, progress);
                        msLimaData.DataStorage.OriginalFilePath = ofd.FileName;
                        exporter.Start();
                    }
                    else
                    {
                        await msLimaData.DataStorage.SetLibraryAsync(ofd.FileName, msLimaData.Parameter.CompoundGroupingKey, progress);
                    }
                }
                finally
                {
                    Mouse.OverrideCursor = null;
                }
            }
        }

        // Keep the synchronous version for backward compatibility
        public static void ImportFile(MsLimaData msLimaData, AutoRepeater exporter)
        {
            var res = MessageBox.Show("Do you want to open with auto saving?", "Ask", MessageBoxButton.YesNo);

            OpenFileDialog ofd = new OpenFileDialog
            {
                Filter = "MSP file(*.msp)|*.msp| MGF file (*.mgf)|*.mgf| Text file (*.txt)|*.txt| all files(*)|*;",
                Title = "Import a library file",
                RestoreDirectory = true,
                Multiselect = false
            };

            if (ofd.ShowDialog() == true)
            {
                Mouse.OverrideCursor = Cursors.Wait;
                if (exporter.ExportTimer.Enabled) exporter.Stop();

                try
                {
                    if (res == MessageBoxResult.Yes)
                    {
                        var dt = DateTime.Now;
                        var newFilePath = System.IO.Path.GetDirectoryName(ofd.FileName) + "\\" + System.IO.Path.GetFileNameWithoutExtension(ofd.FileName) + "_StartMod_"
                            + dt.ToString("yy_MM_dd_HH_mm_ss") + System.IO.Path.GetExtension(ofd.FileName);
                        System.IO.File.Copy(ofd.FileName, newFilePath, true);
                        msLimaData.DataStorage.SetLibrary(newFilePath, msLimaData.Parameter.CompoundGroupingKey);
                        msLimaData.DataStorage.OriginalFilePath = ofd.FileName;
                        exporter.Start();
                    }
                    else
                    {
                        msLimaData.DataStorage.SetLibrary(ofd.FileName, msLimaData.Parameter.CompoundGroupingKey);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error importing file: {ex.Message}\n\nStack trace:\n{ex.StackTrace}",
                                  "Import Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    Mouse.OverrideCursor = null;
                }
            }
        }

        public static void ImportMassBankFile(MsLimaData msLimaData, AutoRepeater exporter)
        {
            var res = MessageBox.Show("Do you want to open with auto saving?", "Ask", MessageBoxButton.YesNo);

            OpenFileDialog ofd = new OpenFileDialog
            {
                Filter = "Text file (*.txt)|*.txt| all files(*)|*;",
                Title = "Import a library file",
                RestoreDirectory = true,
                Multiselect = false
            };

            if (ofd.ShowDialog() == true)
            {
                Mouse.OverrideCursor = Cursors.Wait;

                try
                {
                    if (res == MessageBoxResult.Yes)
                    {
                        var dt = DateTime.Now;
                        var newFilePath = System.IO.Path.GetDirectoryName(ofd.FileName) + "\\" + System.IO.Path.GetFileNameWithoutExtension(ofd.FileName) + "_StartMod_"
                            + dt.ToString("yy_MM_dd_HH_mm_ss") + System.IO.Path.GetExtension(ofd.FileName);
                        System.IO.File.Copy(ofd.FileName, newFilePath, true);
                        msLimaData.DataStorage.SetMassBankLibrary(newFilePath, msLimaData.Parameter.CompoundGroupingKey);
                        msLimaData.DataStorage.OriginalFilePath = ofd.FileName;
                        exporter.Start();
                    }
                    else
                    {
                        msLimaData.DataStorage.SetMassBankLibrary(ofd.FileName, msLimaData.Parameter.CompoundGroupingKey);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error importing file: {ex.Message}\n\nStack trace:\n{ex.StackTrace}",
                                  "Import Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    Mouse.OverrideCursor = null;
                }
            }
        }

    }
}
