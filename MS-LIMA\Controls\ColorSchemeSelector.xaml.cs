using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Metabolomics.MsLima.Services;

namespace Metabolomics.MsLima.Controls
{
    /// <summary>
    /// Color scheme item for the selector
    /// </summary>
    public class ColorSchemeItem
    {
        public ColorScheme ColorScheme { get; set; }
        public string DisplayName { get; set; }
        public Brush PreviewColor { get; set; }

        public override string ToString()
        {
            return DisplayName;
        }
    }

    /// <summary>
    /// Interaction logic for ColorSchemeSelector.xaml
    /// </summary>
    public partial class ColorSchemeSelector : UserControl
    {
        public static readonly DependencyProperty CurrentColorSchemeProperty =
            DependencyProperty.Register("CurrentColorScheme", typeof(ColorScheme), typeof(ColorSchemeSelector),
                new PropertyMetadata(ColorScheme.MaterialBlue, OnCurrentColorSchemeChanged));

        public ColorScheme CurrentColorScheme
        {
            get { return (ColorScheme)GetValue(CurrentColorSchemeProperty); }
            set { SetValue(CurrentColorSchemeProperty, value); }
        }

        public event EventHandler<ColorScheme> ColorSchemeChanged;

        private bool _isUpdatingSelection = false;

        public ColorSchemeSelector()
        {
            InitializeComponent();
            InitializeColorSchemeItems();

            // Subscribe to theme service changes
            if (ThemeService.Instance != null)
            {
                ThemeService.Instance.ColorSchemeChanged += OnThemeServiceColorSchemeChanged;
                CurrentColorScheme = ThemeService.Instance.CurrentColorScheme;
            }

            // Subscribe to unloaded event for cleanup
            this.Unloaded += ColorSchemeSelector_Unloaded;
        }

        private static void OnCurrentColorSchemeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            var control = d as ColorSchemeSelector;
            if (control != null && !control._isUpdatingSelection)
            {
                control.UpdateSelectedColorScheme((ColorScheme)e.NewValue);
            }
        }

        private void OnThemeServiceColorSchemeChanged(object sender, ColorScheme newColorScheme)
        {
            if (CurrentColorScheme != newColorScheme)
            {
                CurrentColorScheme = newColorScheme;
            }
        }

        /// <summary>
        /// Initialize the color scheme items in the ComboBox
        /// </summary>
        private void InitializeColorSchemeItems()
        {
            var colorSchemeItems = new List<ColorSchemeItem>();

            // Add all available color schemes
            foreach (ColorScheme colorScheme in Enum.GetValues(typeof(ColorScheme)))
            {
                var color = ThemeService.Instance.GetAccentColor(colorScheme);
                var colorSchemeItem = new ColorSchemeItem
                {
                    ColorScheme = colorScheme,
                    DisplayName = GetShortColorName(colorScheme),
                    PreviewColor = new SolidColorBrush(color)
                };
                colorSchemeItems.Add(colorSchemeItem);
            }

            ColorSchemeSelectorComboBox.ItemsSource = colorSchemeItems;

            // Use a simpler approach for setting Tag properties
            ColorSchemeSelectorComboBox.ItemContainerGenerator.StatusChanged += OnItemContainerGeneratorStatusChanged;

            // Set initial selection
            UpdateSelectedColorScheme(CurrentColorScheme);
        }

        /// <summary>
        /// Handle item container generation to set Tag properties
        /// </summary>
        private void OnItemContainerGeneratorStatusChanged(object sender, EventArgs e)
        {
            if (ColorSchemeSelectorComboBox.ItemContainerGenerator.Status == System.Windows.Controls.Primitives.GeneratorStatus.ContainersGenerated)
            {
                try
                {
                    var items = ColorSchemeSelectorComboBox.ItemsSource as List<ColorSchemeItem>;
                    if (items != null)
                    {
                        for (int i = 0; i < items.Count; i++)
                        {
                            var container = ColorSchemeSelectorComboBox.ItemContainerGenerator.ContainerFromIndex(i) as ComboBoxItem;
                            if (container != null)
                            {
                                container.Tag = items[i].PreviewColor;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error setting container tags: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Update the selected color scheme in the ComboBox
        /// </summary>
        private void UpdateSelectedColorScheme(ColorScheme colorScheme)
        {
            _isUpdatingSelection = true;
            try
            {
                var colorSchemeItem = ColorSchemeSelectorComboBox.Items.Cast<ColorSchemeItem>()
                    .FirstOrDefault(item => item.ColorScheme == colorScheme);

                if (colorSchemeItem != null)
                {
                    ColorSchemeSelectorComboBox.SelectedItem = colorSchemeItem;
                }
            }
            finally
            {
                _isUpdatingSelection = false;
            }
        }

        /// <summary>
        /// Handle color scheme selection change
        /// </summary>
        private async void ColorSchemeSelectorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"=== COLOR SCHEME SELECTION CHANGED EVENT ===");
            System.Diagnostics.Debug.WriteLine($"_isUpdatingSelection: {_isUpdatingSelection}");
            System.Diagnostics.Debug.WriteLine($"SelectedItem: {ColorSchemeSelectorComboBox.SelectedItem}");

            if (_isUpdatingSelection || ColorSchemeSelectorComboBox.SelectedItem == null)
            {
                System.Diagnostics.Debug.WriteLine("Exiting early - updating selection or no item selected");
                return;
            }

            // Disable the ComboBox temporarily to prevent rapid clicking
            ColorSchemeSelectorComboBox.IsEnabled = false;

            try
            {
                var selectedColorScheme = (ColorSchemeSelectorComboBox.SelectedItem as ColorSchemeItem)?.ColorScheme;
                System.Diagnostics.Debug.WriteLine($"Selected color scheme: {selectedColorScheme}");
                System.Diagnostics.Debug.WriteLine($"Current color scheme: {CurrentColorScheme}");

                if (selectedColorScheme.HasValue && selectedColorScheme.Value != CurrentColorScheme)
                {
                    System.Diagnostics.Debug.WriteLine($"*** FIXED: CHANGING COLOR SCHEME FROM {CurrentColorScheme} TO {selectedColorScheme.Value} ***");

                    // Update the property immediately for responsive UI
                    CurrentColorScheme = selectedColorScheme.Value;
                    System.Diagnostics.Debug.WriteLine($"Updated CurrentColorScheme property");

                    // Notify listeners
                    ColorSchemeChanged?.Invoke(this, selectedColorScheme.Value);
                    System.Diagnostics.Debug.WriteLine($"Invoked ColorSchemeChanged event");

                    // CRITICAL FIX: Update the theme service first
                    if (ThemeService.Instance != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"CRITICAL FIX: ThemeService.Instance found - updating CurrentColorScheme");
                        ThemeService.Instance.CurrentColorScheme = selectedColorScheme.Value;
                        System.Diagnostics.Debug.WriteLine($"CRITICAL FIX: ThemeService.Instance.CurrentColorScheme set to {selectedColorScheme.Value}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("ERROR: ThemeService.Instance is null!");
                    }

                    // CRITICAL FIX: Update ThemeManager to ensure color scheme persistence
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"CRITICAL FIX: Updating ThemeManager.CurrentColorScheme to {selectedColorScheme.Value}");
                        Metabolomics.MsLima.Services.ThemeManager.Instance.CurrentColorScheme = selectedColorScheme.Value;
                        System.Diagnostics.Debug.WriteLine($"CRITICAL FIX: ThemeManager.CurrentColorScheme updated successfully");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"ERROR updating ThemeManager.CurrentColorScheme: {ex.Message}");
                    }

                    // Small delay to ensure UI updates are processed
                    await Task.Delay(100);

                    System.Diagnostics.Debug.WriteLine($"*** FIXED: COMPLETED COLOR SCHEME CHANGE TO {selectedColorScheme.Value} ***");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("No change needed - same color scheme or invalid selection");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CRITICAL ERROR in color scheme selector: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // Reset the selection on error
                UpdateSelectedColorScheme(CurrentColorScheme);
            }
            finally
            {
                // Re-enable the ComboBox after a short delay to prevent rapid clicking
                await Task.Delay(100);
                ColorSchemeSelectorComboBox.IsEnabled = true;
            }
        }

        /// <summary>
        /// Get short color name for compact display - Modern Professional Palette
        /// </summary>
        private string GetShortColorName(ColorScheme scheme)
        {
            switch (scheme)
            {
                case ColorScheme.MaterialBlue:
                    return "Mat Blue";
                case ColorScheme.MaterialIndigo:
                    return "Mat Indigo";
                case ColorScheme.MaterialTeal:
                    return "Mat Teal";
                case ColorScheme.MaterialGreen:
                    return "Mat Green";
                case ColorScheme.VSCodeDarkBlue:
                    return "VS Blue";
                case ColorScheme.VSCodePurple:
                    return "VS Purple";
                case ColorScheme.VSCodeOrange:
                    return "VS Orange";
                case ColorScheme.GitHubGrayBlue:
                    return "Git Gray";
                case ColorScheme.ModernCoral:
                    return "Coral";
                case ColorScheme.ModernMint:
                    return "Mint";
                case ColorScheme.LightPink:
                    return "Pink";
                case ColorScheme.ModCyanate:
                    return "Cyanate";
                default:
                    return "Mat Blue";
            }
        }

        /// <summary>
        /// Cleanup event subscriptions
        /// </summary>
        private void ColorSchemeSelector_Unloaded(object sender, RoutedEventArgs e)
        {
            if (ThemeService.Instance != null)
            {
                ThemeService.Instance.ColorSchemeChanged -= OnThemeServiceColorSchemeChanged;
            }
        }
    }
}
