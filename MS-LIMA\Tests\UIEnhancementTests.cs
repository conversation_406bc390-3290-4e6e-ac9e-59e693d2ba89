using System;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Metabolomics.MsLima;

namespace MS_LIMA.Tests
{
    /// <summary>
    /// Comprehensive test suite for UI enhancement changes
    /// Tests all implemented improvements including layout, styling, and functionality
    /// </summary>
    [TestClass]
    public class UIEnhancementTests
    {
        private MainWindow _mainWindow;
        private Application _app;

        [TestInitialize]
        public void Setup()
        {
            // Initialize WPF application for testing
            if (Application.Current == null)
            {
                _app = new Application();
            }
            
            // Create main window instance
            _mainWindow = new MainWindow();
        }

        [TestCleanup]
        public void Cleanup()
        {
            _mainWindow?.Close();
            _app?.Shutdown();
        }

        /// <summary>
        /// Test 1: Verify column ratio optimization (0.8:1:1.5)
        /// </summary>
        [TestMethod]
        public void Test_ColumnRatioOptimization()
        {
            // Arrange
            _mainWindow.Show();
            var mainGrid = FindChildByName(_mainWindow, "MainGrid") as Grid;
            
            // Act & Assert
            Assert.IsNotNull(mainGrid, "Main grid should exist");
            Assert.AreEqual(3, mainGrid.ColumnDefinitions.Count, "Should have 3 columns");
            
            // Verify column ratios
            var col1 = mainGrid.ColumnDefinitions[0];
            var col2 = mainGrid.ColumnDefinitions[1];
            var col3 = mainGrid.ColumnDefinitions[2];
            
            Assert.AreEqual(new GridLength(0.8, GridUnitType.Star), col1.Width, "Column 1 should be 0.8*");
            Assert.AreEqual(new GridLength(1, GridUnitType.Star), col2.Width, "Column 2 should be 1*");
            Assert.AreEqual(new GridLength(1.5, GridUnitType.Star), col3.Width, "Column 3 should be 1.5*");
        }

        /// <summary>
        /// Test 2: Verify General Information section spacing optimization
        /// </summary>
        [TestMethod]
        public void Test_GeneralInformationSpacing()
        {
            // Arrange
            _mainWindow.Show();
            var generalInfoSection = FindChildByName(_mainWindow, "GeneralInformationSection") as StackPanel;
            
            // Act & Assert
            Assert.IsNotNull(generalInfoSection, "General Information section should exist");
            
            // Check title styling
            var titleBlock = generalInfoSection.Children[0] as TextBlock;
            Assert.IsNotNull(titleBlock, "Title should exist");
            Assert.AreEqual(18, titleBlock.FontSize, "Title font size should be 18");
            Assert.AreEqual(FontWeights.Bold, titleBlock.FontWeight, "Title should be bold");
            Assert.AreEqual(new Thickness(0, 0, 0, 12), titleBlock.Margin, "Title margin should be optimized");
            
            // Check content spacing
            var contentGrid = generalInfoSection.Children[1] as Grid;
            Assert.IsNotNull(contentGrid, "Content grid should exist");
            Assert.AreEqual(8, contentGrid.RowDefinitions[0].Height.Value, "Row spacing should be optimized");
        }

        /// <summary>
        /// Test 3: Verify modern visual effects on title and logo
        /// </summary>
        [TestMethod]
        public void Test_ModernVisualEffects()
        {
            // Arrange
            _mainWindow.Show();
            var titleBar = FindChildByName(_mainWindow, "TitleBar") as Grid;
            
            // Act & Assert
            Assert.IsNotNull(titleBar, "Title bar should exist");
            
            // Test logo enhancements
            var logo = FindChildByType<Image>(titleBar);
            Assert.IsNotNull(logo, "Logo should exist");
            Assert.AreEqual(48, logo.Height, "Logo height should be 48px");
            Assert.AreEqual(48, logo.Width, "Logo width should be 48px");
            Assert.IsNotNull(logo.Effect, "Logo should have drop shadow effect");
            
            // Test title gradient and effects
            var titleText = FindChildByType<TextBlock>(titleBar);
            Assert.IsNotNull(titleText, "Title text should exist");
            Assert.IsInstanceOfType(titleText.Foreground, typeof(LinearGradientBrush), "Title should have gradient brush");
            Assert.IsNotNull(titleText.Effect, "Title should have drop shadow effect");
            
            var gradientBrush = titleText.Foreground as LinearGradientBrush;
            Assert.AreEqual(2, gradientBrush.GradientStops.Count, "Gradient should have 2 stops");
        }

        /// <summary>
        /// Test 4: Verify double-click maximize/restore functionality
        /// </summary>
        [TestMethod]
        public void Test_DoubleClickMaximizeRestore()
        {
            // Arrange
            _mainWindow.Show();
            var titleBar = FindChildByName(_mainWindow, "TitleBar") as Grid;
            var initialState = _mainWindow.WindowState;
            
            // Act - Simulate double-click on title bar
            var doubleClickArgs = new MouseButtonEventArgs(Mouse.PrimaryDevice, 0, MouseButton.Left)
            {
                RoutedEvent = Control.MouseLeftButtonDownEvent,
                ClickCount = 2
            };
            
            titleBar.RaiseEvent(doubleClickArgs);
            
            // Assert
            Assert.AreNotEqual(initialState, _mainWindow.WindowState, "Window state should change after double-click");
            
            // Test toggle functionality
            titleBar.RaiseEvent(doubleClickArgs);
            Assert.AreEqual(initialState, _mainWindow.WindowState, "Window state should return to original after second double-click");
        }

        /// <summary>
        /// Test 5: Verify maximize button content updates
        /// </summary>
        [TestMethod]
        public void Test_MaximizeButtonContentUpdate()
        {
            // Arrange
            _mainWindow.Show();
            var maximizeButton = FindChildByName(_mainWindow, "MaximizeButton") as Button;
            
            // Act & Assert
            Assert.IsNotNull(maximizeButton, "Maximize button should exist");
            
            // Test normal state
            _mainWindow.WindowState = WindowState.Normal;
            Assert.AreEqual("🗖", maximizeButton.Content.ToString(), "Button should show maximize icon in normal state");
            
            // Test maximized state
            _mainWindow.WindowState = WindowState.Maximized;
            Assert.AreEqual("🗗", maximizeButton.Content.ToString(), "Button should show restore icon in maximized state");
        }

        /// <summary>
        /// Test 6: Comprehensive layout validation
        /// </summary>
        [TestMethod]
        public void Test_ComprehensiveLayoutValidation()
        {
            // Arrange
            _mainWindow.Show();
            _mainWindow.Width = 1200;
            _mainWindow.Height = 800;
            
            // Force layout update
            _mainWindow.UpdateLayout();
            
            // Act & Assert
            var mainGrid = FindChildByName(_mainWindow, "MainGrid") as Grid;
            Assert.IsNotNull(mainGrid, "Main grid should exist");
            
            // Verify all columns are properly sized
            Assert.IsTrue(mainGrid.ActualWidth > 0, "Grid should have actual width");
            Assert.IsTrue(mainGrid.ActualHeight > 0, "Grid should have actual height");
            
            // Verify no layout errors
            Assert.IsFalse(mainGrid.IsArrangeValid == false, "Layout should be valid");
        }

        /// <summary>
        /// Helper method to find child control by name
        /// </summary>
        private FrameworkElement FindChildByName(DependencyObject parent, string name)
        {
            if (parent == null) return null;
            
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is FrameworkElement element && element.Name == name)
                    return element;
                
                var result = FindChildByName(child, name);
                if (result != null) return result;
            }
            return null;
        }

        /// <summary>
        /// Helper method to find child control by type
        /// </summary>
        private T FindChildByType<T>(DependencyObject parent) where T : class
        {
            if (parent == null) return null;
            
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;
                
                var nestedResult = FindChildByType<T>(child);
                if (nestedResult != null) return nestedResult;
            }
            return null;
        }
    }
}
