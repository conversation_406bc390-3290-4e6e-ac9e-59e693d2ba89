<UserControl x:Class="Metabolomics.MsLima.Controls.ColorSchemeSelector"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="32" d:DesignWidth="160">

    <UserControl.Resources>
        <!-- Color Scheme ComboBox Style - Enhanced Width with Neutral Grey Text Color -->
        <Style x:Key="ColorSchemeSelectorStyle" TargetType="ComboBox">
            <Setter Property="Width" Value="110"/>
            <Setter Property="Height" Value="29"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="6,2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <!-- Grey text color for theme combo -->
            <Setter Property="Foreground" Value="#808080"/>
            <!-- Simple template that ensures dropdown functionality -->
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <!-- Main Border with rounded corners -->
                            <Border x:Name="MainBorder"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="14.5"
                                    SnapsToDevicePixels="True">
                                <!-- Toggle Button for dropdown -->
                                <ToggleButton x:Name="ToggleButton"
                                              Grid.Column="2"
                                              Focusable="False"
                                              IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                              ClickMode="Press"
                                              Background="{DynamicResource DataGridHeaderGradientBrush}"
                                              BorderThickness="0">
                                    <ToggleButton.Template>
                                        <ControlTemplate TargetType="ToggleButton">
                                            <Grid Background="Transparent">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="20"/>
                                                </Grid.ColumnDefinitions>
                                                <!-- Content Area - Centered Text with Theme-aware Grey/White Color -->
                                                <ContentPresenter x:Name="ContentSite"
                                                                  Grid.Column="0"
                                                                  IsHitTestVisible="False"
                                                                  Content="{Binding SelectionBoxItem, RelativeSource={RelativeSource AncestorType=ComboBox}}"
                                                                  ContentTemplate="{Binding SelectionBoxItemTemplate, RelativeSource={RelativeSource AncestorType=ComboBox}}"
                                                                  Margin="4,2"
                                                                  VerticalAlignment="Center"
                                                                  HorizontalAlignment="Center"
                                                                  TextElement.Foreground="{DynamicResource SecondaryTextBrush}"/>
                                                <!-- Dropdown Arrow -->
                                                <Path x:Name="Arrow"
                                                      Grid.Column="1"
                                                      Data="M 0 0 L 3 3 L 6 0 Z"
                                                      Fill="{DynamicResource SecondaryTextBrush}"
                                                      HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"
                                                      Margin="0,0,6,0"/>
                                            </Grid>
                                        </ControlTemplate>
                                    </ToggleButton.Template>
                                </ToggleButton>
                            </Border>

                            <!-- Popup for dropdown items -->
                            <Popup x:Name="Popup"
                                   Placement="Bottom"
                                   IsOpen="{TemplateBinding IsDropDownOpen}"
                                   AllowsTransparency="True"
                                   Focusable="False"
                                   PopupAnimation="Slide">
                                <Grid x:Name="DropDown"
                                      SnapsToDevicePixels="True"
                                      MinWidth="{TemplateBinding ActualWidth}"
                                      MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                    <Border x:Name="DropDownBorder"
                                            Background="{DynamicResource SurfaceBrush}"
                                            BorderBrush="{DynamicResource BorderBrush}"
                                            BorderThickness="1"
                                            CornerRadius="8">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
                                        </Border.Effect>
                                        <ScrollViewer Margin="4,6,4,6"
                                                      SnapsToDevicePixels="True">
                                            <StackPanel IsItemsHost="True"
                                                        KeyboardNavigation.DirectionalNavigation="Contained"/>
                                        </ScrollViewer>
                                    </Border>
                                </Grid>
                            </Popup>
                        </Grid>

                        <ControlTemplate.Triggers>
                            <!-- Hover State -->
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="MainBorder" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="8" ShadowDepth="2"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <!-- Background half intensity of table headers -->
            <Setter Property="Background" Value="{DynamicResource ColorSchemeSelectorBrush}"/>
            <!-- Grey text color for button text in both themes -->
            <Setter Property="Foreground" Value="#808080"/>
        </Style>

        <!-- Simple ComboBox Item Style with Neutral Grey Text -->
        <Style x:Key="ColorSchemeItemStyle" TargetType="ComboBoxItem">
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="0"/>
            <Setter Property="Background" Value="Transparent"/>
            <!-- Theme-aware text color for dropdown items -->
            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBoxItem">
                        <Border x:Name="ItemBorder"
                                Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}"
                                SnapsToDevicePixels="True">
                            <StackPanel Orientation="Horizontal">
                                <!-- Color Preview Circle -->
                                <Ellipse Width="12" Height="12"
                                         Fill="{Binding Tag, RelativeSource={RelativeSource TemplatedParent}}"
                                         Stroke="{DynamicResource BorderBrush}"
                                         StrokeThickness="1"
                                         VerticalAlignment="Center"
                                         Margin="0,0,8,0"/>
                                <!-- Color Name -->
                                <ContentPresenter Content="{TemplateBinding Content}"
                                                  VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ItemBorder" Property="Background" Value="{DynamicResource HoverBrush}"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="ItemBorder" Property="Background" Value="{DynamicResource SelectedRowBrush}"/>
                                <!-- Keep theme-aware text color -->
                                <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <!-- Simplified Color Scheme ComboBox -->
        <ComboBox x:Name="ColorSchemeSelectorComboBox"
                  Style="{StaticResource ColorSchemeSelectorStyle}"
                  ItemContainerStyle="{StaticResource ColorSchemeItemStyle}"
                  SelectionChanged="ColorSchemeSelectorComboBox_SelectionChanged"
                  VerticalAlignment="Center"
                  HorizontalAlignment="Left"/>
    </Grid>
</UserControl>
