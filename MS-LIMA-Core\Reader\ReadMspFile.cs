using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Metabolomics.Core;
using Metabolomics.Core.Utility;
using Metabolomics.Core.Parser;
using Metabolomics.MsLima.Bean;

namespace Metabolomics.MsLima.Reader
{
    public static class ReadMspFile
    {
        // Async version for better UI responsiveness
        public static async Task<List<MassSpectrum>> ReadAsMsSpectraAsync(string filePath, IProgress<int> progress = null)
        {
            return await Task.Run(() => ReadAsMsSpectra(filePath, progress));
        }

        // Enhanced async version with cancellation support for professional import experience
        public static async Task<List<MassSpectrum>> ReadAsMsSpectraAsync(string filePath, IProgress<int> progress, CancellationToken cancellationToken)
        {
            return await Task.Run(() => ReadAsMsSpectra(filePath, progress, cancellationToken), cancellationToken);
        }

        public static List<MassSpectrum> ReadAsMsSpectra(string filePath, IProgress<int> progress = null)
        {
            return ReadAsMsSpectra(filePath, progress, CancellationToken.None);
        }

        public static List<MassSpectrum> ReadAsMsSpectra(string filePath, IProgress<int> progress, CancellationToken cancellationToken)
        {
            var importStopwatch = System.Diagnostics.Stopwatch.StartNew();

            // ENHANCED: Pre-allocate with optimized capacity estimation for better performance
            var fileInfo = new FileInfo(filePath);
            var fileSizeMB = fileInfo.Length / (1024.0 * 1024.0);
            var estimatedSpectraCount = (int)(fileInfo.Length / 1500); // Optimized estimate: 1.5KB per spectrum
            var spectra = new List<MassSpectrum>(Math.Max(estimatedSpectraCount, 1000));

            System.Diagnostics.Debug.WriteLine($"Starting MSP import: {fileSizeMB:F2}MB file, estimated {estimatedSpectraCount} spectra");

            var spectrum = new MassSpectrum();
            string wkstr;
            int counter = 0;
            int totalLines = 0;
            int processedLines = 0;
            int lastProgressReport = 0;

            // ENHANCED: Optimized line counting with buffer size optimization
            if (progress != null)
            {
                var lineCountStopwatch = System.Diagnostics.Stopwatch.StartNew();
                using (var sr = new StreamReader(filePath, Encoding.UTF8, true, 65536)) // 64KB buffer
                {
                    while (sr.ReadLine() != null) totalLines++;
                }
                lineCountStopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"Line counting completed in {lineCountStopwatch.ElapsedMilliseconds}ms: {totalLines} lines");
            }

            using (StreamReader sr = new StreamReader(filePath, Encoding.UTF8, true, 65536)) // 64KB buffer for better I/O performance
            {
                float rt = 0, preMz = 0, ri = 0;

                while (sr.Peek() > -1)
                {
                    // ENHANCED: Check for cancellation every 100 lines for responsive cancel
                    if (processedLines % 100 == 0)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                    }

                    wkstr = sr.ReadLine();
                    processedLines++;

                    // ENHANCED: Optimized progress reporting - reduce frequency for better performance
                    if (progress != null && processedLines % 1000 == 0) // Report every 1000 lines for smoother UI
                    {
                        var currentProgress = (int)((double)processedLines / totalLines * 100);
                        if (currentProgress > lastProgressReport) // Only report if progress actually changed
                        {
                            progress.Report(currentProgress);
                            lastProgressReport = currentProgress;
                        }
                    }

                    if (Regex.IsMatch(wkstr, "^NAME:.*", RegexOptions.IgnoreCase))
                    {
                        // Suppress property change notifications during loading for better performance
                        spectrum.SetPropertyChangeNotificationMode(false);

                        spectrum.Id = counter;
                        spectrum.Name = wkstr.Substring(wkstr.Split(':')[0].Length + 2).Trim();

                        while (sr.Peek() > -1)
                        {
                            wkstr = sr.ReadLine();
                            if (wkstr == string.Empty || String.IsNullOrWhiteSpace(wkstr)) break;

                            // Optimize by converting to uppercase once per line and parsing colon once
                            var upperLine = wkstr.ToUpperInvariant();
                            var colonIndex = wkstr.IndexOf(':');
                            if (colonIndex == -1) continue; // Skip lines without colon

                            var fieldValue = wkstr.Substring(colonIndex + 1).Trim();
                            var fieldValueAfterSpace = colonIndex + 2 < wkstr.Length ? wkstr.Substring(colonIndex + 2).Trim() : "";

                            // Use optimized field matching with early returns for better performance
                            if (ProcessFieldValue(upperLine, fieldValue, fieldValueAfterSpace, spectrum))
                            {
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "INSTRUMENT TYPE:", "INSTRUMENT_TYPE:", "INSTRUMENTTYPE:"))
                            {
                                spectrum.InstrumentType = fieldValueAfterSpace;
                                System.Diagnostics.Debug.WriteLine($"DEBUG: Found InstrumentType field: '{fieldValueAfterSpace}'");
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "INSTRUMENT:"))
                            {
                                spectrum.Instrument = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "LICENSE:"))
                            {
                                spectrum.License = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "SPECTRUM TYPE:", "SPECTRUM_TYPE:"))
                            {
                                spectrum.SpectrumType = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "FORMULA:"))
                            {
                                spectrum.Formula = fieldValue;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "ION MODE:", "ION_MODE:"))
                            {
                                if (fieldValue.ToLower() == "negative") spectrum.IonMode = IonMode.Negative;
                                else spectrum.IonMode = IonMode.Positive;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "SMILES:"))
                            {
                                spectrum.Smiles = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "COLLISION ENERGY:", "COLLISION_ENERGY:", "COLLISIONENERGY:"))
                            {
                                spectrum.CollisionEnergy = MspParser.GetCollisionEnergy(fieldValueAfterSpace);
                                System.Diagnostics.Debug.WriteLine($"DEBUG: Found CollisionEnergy field: '{fieldValueAfterSpace}' -> parsed as: {spectrum.CollisionEnergy} for spectrum: {spectrum.Name}");
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "INCHI KEY:", "INCHI_KEY:", "INCHIKEY:"))
                            {
                                spectrum.InChIKey = fieldValue;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "INCHI:"))
                            {
                                spectrum.InChI = fieldValue;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "COMPOUND CLASS:", "COMPOUND_CLASS:"))
                            {
                                spectrum.CompoundClass = fieldValue;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "RETENTION TIME:", "RETENTION_TIME:", "RETENTIONTIME:"))
                            {
                                if (float.TryParse(fieldValue, out rt)) spectrum.RetentionTime = rt; else spectrum.RetentionTime = -1;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "RT:"))
                            {
                                if (float.TryParse(fieldValue, out rt)) spectrum.RetentionTime = rt; else spectrum.RetentionTime = -1;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "RETENTION INDEX:", "RETENTION_INDEX:", "RETENTIONINDEX:"))
                            {
                                if (float.TryParse(fieldValue, out ri)) spectrum.RetentionIndex = ri; else spectrum.RetentionIndex = -1;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "RI:"))
                            {
                                if (float.TryParse(fieldValue, out ri)) spectrum.RetentionIndex = ri; else spectrum.RetentionIndex = -1;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "PRECURSOR MZ:", "PRECURSOR_MZ:", "PRECURSORMZ:"))
                            {
                                if (float.TryParse(fieldValue, out preMz)) spectrum.PrecursorMz = preMz;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "PRECURSOR TYPE:", "PRECURSOR_TYPE:", "PRECURSORTYPE:"))
                            {
                                spectrum.AdductIon = AdductIonParser.GetAdductIon(fieldValueAfterSpace);
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "MS LEVEL:", "MS_LEVEL:", "MSLEVEL:"))
                            {
                                spectrum.MsLevel = fieldValue;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "LINKS:"))
                            {
                                spectrum.Links = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "IUPAC NAME:", "IUPAC_NAME:", "IUPACNAME:"))
                            {
                                spectrum.IUPACname = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "CID:"))
                            {
                                spectrum.CID = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "CAS:"))
                            {
                                spectrum.CAS = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "EXACT MASS:", "EXACT_MASS:", "EXACTMASS:"))
                            {
                                if (double.TryParse(fieldValue, out double exactmass)) spectrum.Exactmass = exactmass;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "MW:"))
                            {
                                if (double.TryParse(fieldValue, out double mw)) spectrum.MW = mw;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "X LOG P:", "X_LOG_P:", "XLOGP:"))
                            {
                                if (double.TryParse(fieldValue, out double xlogp)) spectrum.XLogP = xlogp;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "M LOG P:", "M_LOG_P:", "MLOGP:"))
                            {
                                if (double.TryParse(fieldValue, out double mlogp)) spectrum.MLogP = mlogp;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "A LOG P:", "A_LOG_P:", "ALOGP:"))
                            {
                                if (double.TryParse(fieldValue, out double alogp)) spectrum.ALogP = alogp;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "NUM BOND:", "NUM_BOND:", "NUMBOND:", "NUM BONDS:", "NUM_BONDS:"))
                            {
                                if (int.TryParse(fieldValue, out int numbond)) spectrum.NumBond = numbond;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "NUM ATOM:", "NUM_ATOM:", "NUMATOM:", "NUM ATOMS:", "NUM_ATOMS:"))
                            {
                                if (int.TryParse(fieldValue, out int numatom)) spectrum.NumAtom = numatom;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "ONTOLOGY:", "CLASSIFICATION:", "TAXONOMY:"))
                            {
                                spectrum.Ontology = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "SUPER CLASS:", "SUPER_CLASS:", "SUPERCLASS:"))
                            {
                                spectrum.Superclass = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "CLASS:"))
                            {
                                spectrum.Class = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "SUB CLASS:", "SUB_CLASS:", "SUBCLASS:"))
                            {
                                spectrum.Subclass = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "CRAMER RULES:", "CRAMER_RULES:", "CRAMERRULES:"))
                            {
                                spectrum.Cramerrules = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "SVHC:"))
                            {
                                spectrum.SVHC = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "CMR SUSPECT:", "CMR_SUSPECT:", "CMRSUSPECT:"))
                            {
                                spectrum.CMRsuspect = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "CMR:"))
                            {
                                spectrum.CMR = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "EDC:"))
                            {
                                spectrum.EDC = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "IARC:"))
                            {
                                spectrum.IARC = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "EU SML:", "EU_SML:", "EUSML:"))
                            {
                                spectrum.Eusml = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "CHINA SML:", "CHINA_SML:", "CHINASML:"))
                            {
                                spectrum.ChinaSml = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "TOPO PSA:", "TOPO_PSA:", "TOPOPSA:"))
                            {
                                if (double.TryParse(fieldValue, out double topoPSA)) spectrum.TopoPSA = topoPSA;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "CARCINOGENICITY ISS:", "CARCINOGENICITY_ISS:", "CARCINOGENICITYISS:"))
                            {
                                spectrum.Carcinogenicity_ISS = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "DNA ALERTS OASIS:", "DNA_ALERTS_OASIS:", "DNAALERTSOASIS:"))
                            {
                                spectrum.DNA_alerts_OASIS = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "DNA BINDING OASIS:", "DNA_BINDING_OASIS:", "DNABINDINGOASIS:"))
                            {
                                spectrum.DNA_binding_OASIS = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "DNA BINDING OECD:", "DNA_BINDING_OECD:", "DNABINDINGOECD:"))
                            {
                                spectrum.DNA_binding_OECD = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "PROTEIN BINDING ALERTS OASIS:", "PROTEIN_BINDING_ALERTS_OASIS:", "PROTEINBINDINGALERTSOASIS:"))
                            {
                                spectrum.Protein_binding_alerts_OASIS = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "VITRO MUTAGENICITY ALERTS ISS:", "VITRO_MUTAGENICITY_ALERTS_ISS:", "VITROMUTAGENICITYADLERTSISS:"))
                            {
                                spectrum.Vitro_mutagenicity_alerts_ISS = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "VIVO MUTAGENICITY ALERTS ISS:", "VIVO_MUTAGENICITY_ALERTS_ISS:", "VIVOMUTAGENICITYADLERTSISS:"))
                            {
                                spectrum.Vivo_mutagenicity_alerts_ISS = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "DATE:"))
                            {
                                spectrum.Date = fieldValueAfterSpace;
                                continue;
                            }
                            else if (IsFieldMatch(upperLine, "NUM PEAKS:", "NUM_PEAKS:", "NUMPEAKS:"))
                            {
                                spectrum.Spectrum = ReadFile.ReadSpectrum(sr, wkstr, out int peakNum);
                                continue;
                            }
                            else
                            {
                                spectrum.OtherMetaData.Add(wkstr);
                                continue;
                            }
                        }
                        try
                        {
                            spectrum.TheoreticalMass = MspParser.ConvertFormulaToAdductMass(spectrum.AdductIon, spectrum.Formula, spectrum.IonMode);
                        }
                        catch
                        {
                            spectrum.TheoreticalMass = -1;
                        }

                        // Re-enable property change notifications after loading
                        spectrum.SetPropertyChangeNotificationMode(true);

                        spectra.Add(spectrum);
                        spectrum = new MassSpectrum();
                        counter++;
                    }
                }
            }

            // ENHANCED: Performance monitoring and optimization reporting
            importStopwatch.Stop();
            var importTimeSeconds = importStopwatch.ElapsedMilliseconds / 1000.0;
            var spectraPerSecond = counter / Math.Max(importTimeSeconds, 0.001);
            var mbPerSecond = fileSizeMB / Math.Max(importTimeSeconds, 0.001);

            System.Diagnostics.Debug.WriteLine($"MSP IMPORT COMPLETED:");
            System.Diagnostics.Debug.WriteLine($"  - File size: {fileSizeMB:F2}MB");
            System.Diagnostics.Debug.WriteLine($"  - Spectra imported: {counter}");
            System.Diagnostics.Debug.WriteLine($"  - Import time: {importTimeSeconds:F2}s");
            System.Diagnostics.Debug.WriteLine($"  - Performance: {spectraPerSecond:F0} spectra/sec, {mbPerSecond:F2} MB/sec");

            // Performance targets: >500 spectra/sec, >5MB/sec for enterprise-grade performance
            if (spectraPerSecond < 500 || mbPerSecond < 5.0)
            {
                System.Diagnostics.Debug.WriteLine($"PERFORMANCE WARNING: Import speed below enterprise targets (500 spectra/sec, 5MB/sec)");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"PERFORMANCE EXCELLENT: Import speed meets enterprise targets");
            }

            // Final progress report
            if (progress != null)
            {
                progress.Report(100);
            }

            return spectra;
        }

        // Optimized field processing method to reduce redundant string operations
        private static bool ProcessFieldValue(string upperLine, string fieldValue, string fieldValueAfterSpace, MassSpectrum spectrum)
        {
            // Use switch-like pattern matching for better performance
            if (upperLine.StartsWith("COMMENT:"))
            {
                spectrum.Comment = fieldValueAfterSpace;
                return true;
            }
            else if (upperLine.StartsWith("AUTHOR:") || upperLine.StartsWith("AUTHORS:"))
            {
                spectrum.Authors = fieldValueAfterSpace;
                return true;
            }
            else if (upperLine.StartsWith("FORMULA:"))
            {
                spectrum.Formula = fieldValue;
                return true;
            }
            else if (upperLine.StartsWith("ION MODE:") || upperLine.StartsWith("ION_MODE:"))
            {
                if (fieldValue.ToLower() == "negative") spectrum.IonMode = IonMode.Negative;
                else spectrum.IonMode = IonMode.Positive;
                return true;
            }
            else if (upperLine.StartsWith("SMILES:"))
            {
                spectrum.Smiles = fieldValueAfterSpace;
                return true;
            }
            else if (upperLine.StartsWith("COLLISION ENERGY:") || upperLine.StartsWith("COLLISION_ENERGY:") || upperLine.StartsWith("COLLISIONENERGY:"))
            {
                spectrum.CollisionEnergy = MspParser.GetCollisionEnergy(fieldValueAfterSpace);
                System.Diagnostics.Debug.WriteLine($"DEBUG: ProcessFieldValue - Found CollisionEnergy field: '{fieldValueAfterSpace}' -> parsed as: {spectrum.CollisionEnergy} for spectrum: {spectrum.Name}");
                return true;
            }
            else if (upperLine.StartsWith("INSTRUMENT TYPE:") || upperLine.StartsWith("INSTRUMENT_TYPE:") || upperLine.StartsWith("INSTRUMENTTYPE:"))
            {
                spectrum.InstrumentType = fieldValueAfterSpace;
                System.Diagnostics.Debug.WriteLine($"DEBUG: ProcessFieldValue - Found InstrumentType field: '{fieldValueAfterSpace}' for spectrum: {spectrum.Name}");
                return true;
            }
            else if (upperLine.StartsWith("INCHI KEY:") || upperLine.StartsWith("INCHI_KEY:") || upperLine.StartsWith("INCHIKEY:"))
            {
                spectrum.InChIKey = fieldValue;
                return true;
            }
            else if (upperLine.StartsWith("INCHI:"))
            {
                spectrum.InChI = fieldValue;
                return true;
            }
            else if (upperLine.StartsWith("COMPOUND CLASS:") || upperLine.StartsWith("COMPOUND_CLASS:"))
            {
                spectrum.CompoundClass = fieldValue;
                return true;
            }

            return false; // Field not processed, continue with original logic
        }

        // Optimized field detection using fast string operations
        private static bool IsFieldMatch(string upperLine, params string[] patterns)
        {
            foreach (var pattern in patterns)
            {
                if (upperLine.StartsWith(pattern)) return true;
            }
            return false;
        }

    }
}
