using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using Microsoft.Win32;

namespace Metabolomics.MsLima.Services
{
    public class ThemeManager
    {
        private static ThemeManager _instance;
        private static readonly object _lock = new object();

        public static ThemeManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new ThemeManager();
                    }
                }
                return _instance;
            }
        }

        public event EventHandler<bool> ThemeChanged;

        private bool _isDarkTheme;
        private ColorScheme _colorScheme = ColorScheme.MaterialBlue;
        private const string THEME_SETTING_KEY = "MS-LIMA_Theme";
        private const string COLOR_SCHEME_SETTING_KEY = "MS-LIMA_ColorScheme";
        private const string REGISTRY_KEY = @"SOFTWARE\MS-LIMA";

        public bool IsDarkTheme
        {
            get { return _isDarkTheme; }
            private set
            {
                if (_isDarkTheme != value)
                {
                    _isDarkTheme = value;
                    ThemeChanged?.Invoke(this, value);
                }
            }
        }

        public ColorScheme CurrentColorScheme
        {
            get { return _colorScheme; }
            set
            {
                if (_colorScheme != value)
                {
                    _colorScheme = value;
                    ThemeService.Instance.CurrentColorScheme = value;
                    SaveColorSchemePreference(value);
                }
            }
        }

        private ThemeManager()
        {
            LoadThemePreference();
            LoadColorSchemePreference();
        }

        public void Initialize()
        {
            // Detect system theme if no preference is saved
            if (!HasSavedThemePreference())
            {
                IsDarkTheme = IsSystemDarkTheme();
            }

            ApplyTheme(IsDarkTheme);
        }

        /// <summary>
        /// NON-BLOCKING theme toggle - prevents UI freezing
        /// </summary>
        public async Task ToggleThemeAsync()
        {
            await SetThemeAsync(!IsDarkTheme);
        }

        /// <summary>
        /// SYNCHRONOUS theme toggle for backward compatibility
        /// </summary>
        public void ToggleTheme()
        {
            Task.Run(async () => await SetThemeAsync(!IsDarkTheme));
        }

        /// <summary>
        /// NON-BLOCKING theme setting - prevents UI freezing
        /// </summary>
        public async Task SetThemeAsync(bool isDarkTheme)
        {
            IsDarkTheme = isDarkTheme;
            await ApplyThemeAsync(isDarkTheme);
            await SaveThemePreferenceAsync(isDarkTheme);
        }

        /// <summary>
        /// SYNCHRONOUS theme setting for backward compatibility
        /// </summary>
        public void SetTheme(bool isDarkTheme)
        {
            Task.Run(async () => await SetThemeAsync(isDarkTheme));
        }

        /// <summary>
        /// FIXED THEME APPLICATION - Properly integrates with ThemeService for dynamic color schemes
        /// </summary>
        private async Task ApplyThemeAsync(bool isDarkTheme)
        {
            var themeStopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                System.Diagnostics.Debug.WriteLine($"Starting FIXED theme application: {(isDarkTheme ? "Dark" : "Light")} theme");

                // PHASE 1: Apply base theme resources (UI thread)
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    var app = Application.Current;
                    if (app?.Resources == null) return;

                    System.Diagnostics.Debug.WriteLine($"Current merged dictionaries count: {app.Resources.MergedDictionaries.Count}");

                    // ENHANCED: Memory-efficient resource dictionary management
                    var resourcesToRemove = new string[] { "LightTheme", "DarkTheme", "ModernStyles" };
                    var removedDictionaries = new List<ResourceDictionary>();

                    for (int i = app.Resources.MergedDictionaries.Count - 1; i >= 0; i--)
                    {
                        var dict = app.Resources.MergedDictionaries[i];
                        var source = dict.Source?.OriginalString;

                        if (!string.IsNullOrEmpty(source))
                        {
                            foreach (var resourceKey in resourcesToRemove)
                            {
                                if (source.Contains(resourceKey))
                                {
                                    System.Diagnostics.Debug.WriteLine($"Removing resource dictionary: {source}");
                                    removedDictionaries.Add(dict);
                                    app.Resources.MergedDictionaries.RemoveAt(i);
                                    break;
                                }
                            }
                        }
                    }

                    // ENHANCED: Explicit disposal of removed dictionaries to prevent memory leaks
                    foreach (var dict in removedDictionaries)
                    {
                        try
                        {
                            dict.Clear();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Warning: Could not clear resource dictionary: {ex.Message}");
                        }
                    }

                    // ENHANCED: Apply theme and styles with error recovery
                    try
                    {
                        // 1. Apply the theme dictionary first
                        var themeUri = new Uri($"pack://application:,,,/Themes/{(isDarkTheme ? "DarkTheme" : "LightTheme")}.xaml");
                        var themeDict = new ResourceDictionary { Source = themeUri };
                        app.Resources.MergedDictionaries.Add(themeDict);
                        System.Diagnostics.Debug.WriteLine($"Applied theme dictionary: {themeUri}");

                        // 2. Apply ModernStyles.xaml to ensure all controls get styled
                        var stylesUri = new Uri("pack://application:,,,/Themes/ModernStyles.xaml");
                        var stylesDict = new ResourceDictionary { Source = stylesUri };
                        app.Resources.MergedDictionaries.Add(stylesDict);
                        System.Diagnostics.Debug.WriteLine($"Applied styles dictionary: {stylesUri}");

                        System.Diagnostics.Debug.WriteLine($"Final merged dictionaries count: {app.Resources.MergedDictionaries.Count}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error loading theme resources: {ex.Message}");
                        throw;
                    }
                }, System.Windows.Threading.DispatcherPriority.Send); // Use Send for immediate, synchronous execution

                // CRITICAL FIX: Update ThemeService to trigger dynamic color scheme updates
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"CRITICAL FIX: Updating ThemeService base theme to {(isDarkTheme ? "Dark" : "Light")}");

                        // Update ThemeService which will trigger all the dynamic resource updates
                        ThemeService.Instance.IsDarkTheme = isDarkTheme;

                        System.Diagnostics.Debug.WriteLine($"ThemeService updated - dynamic resources should now be applied");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error updating ThemeService: {ex.Message}");
                    }
                }, System.Windows.Threading.DispatcherPriority.Send);

                // PERFORMANCE OPTIMIZATION: Lightweight UI refresh - no expensive visual tree traversal
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    foreach (Window window in Application.Current.Windows)
                    {
                        try
                        {
                            // LIGHTWEIGHT: Only invalidate visual - WPF handles resource binding updates automatically
                            window.InvalidateVisual();
                            System.Diagnostics.Debug.WriteLine($"Invalidated window: {window.GetType().Name}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error invalidating window {window.GetType().Name}: {ex.Message}");
                        }
                    }
                }, System.Windows.Threading.DispatcherPriority.Background);

                themeStopwatch.Stop();
                System.Diagnostics.Debug.WriteLine($"OPTIMIZED theme application completed in {themeStopwatch.ElapsedMilliseconds}ms: {(isDarkTheme ? "Dark" : "Light")} theme");

                // Performance target: <200ms regardless of data size
                if (themeStopwatch.ElapsedMilliseconds > 200)
                {
                    System.Diagnostics.Debug.WriteLine($"PERFORMANCE WARNING: Theme application took {themeStopwatch.ElapsedMilliseconds}ms - target is <200ms");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CRITICAL ERROR in theme application: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // ENHANCED: Robust fallback with error recovery
                try
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        // Fallback: try to apply light theme
                        var fallbackUri = new Uri("pack://application:,,,/Themes/LightTheme.xaml");
                        var fallbackDict = new ResourceDictionary { Source = fallbackUri };
                        Application.Current.Resources.MergedDictionaries.Add(fallbackDict);
                        System.Diagnostics.Debug.WriteLine("Applied fallback light theme");
                    });
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"CRITICAL: Even fallback theme failed: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// OPTIMIZED visual tree refresh with performance improvements
        /// </summary>
        private void RefreshVisualTreeOptimized(DependencyObject parent)
        {
            if (parent == null) return;

            try
            {
                // ENHANCED: Batch visual updates for better performance
                if (parent is FrameworkElement element)
                {
                    // Use BeginInvoke for non-blocking updates
                    Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        element.InvalidateVisual();
                    }), System.Windows.Threading.DispatcherPriority.Background);
                }

                // OPTIMIZED: Limit recursion depth to prevent performance issues
                int childCount = VisualTreeHelper.GetChildrenCount(parent);
                if (childCount > 0 && childCount < 100) // Prevent excessive recursion
                {
                    for (int i = 0; i < childCount; i++)
                    {
                        var child = VisualTreeHelper.GetChild(parent, i);
                        RefreshVisualTreeOptimized(child);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in optimized visual tree refresh: {ex.Message}");
            }
        }

        /// <summary>
        /// Recursively refreshes the visual tree to ensure all elements update
        /// </summary>
        private void RefreshVisualTree(DependencyObject parent)
        {
            if (parent == null) return;

            try
            {
                // Force refresh of this element
                if (parent is FrameworkElement element)
                {
                    element.InvalidateVisual();
                    element.UpdateLayout();
                }

                // Recursively refresh children
                int childCount = VisualTreeHelper.GetChildrenCount(parent);
                for (int i = 0; i < childCount; i++)
                {
                    var child = VisualTreeHelper.GetChild(parent, i);
                    RefreshVisualTree(child);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing visual tree element: {ex.Message}");
            }
        }

        /// <summary>
        /// SYNCHRONOUS theme application for backward compatibility
        /// </summary>
        private void ApplyTheme(bool isDarkTheme)
        {
            Task.Run(async () => await ApplyThemeAsync(isDarkTheme));
        }

        private bool IsSystemDarkTheme()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Themes\Personalize"))
                {
                    var value = key?.GetValue("AppsUseLightTheme");
                    if (value is int intValue)
                    {
                        return intValue == 0; // 0 = dark theme, 1 = light theme
                    }
                }
            }
            catch
            {
                // If we can't read the registry, default to light theme
            }
            return false;
        }

        private bool HasSavedThemePreference()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    return key?.GetValue(THEME_SETTING_KEY) != null;
                }
            }
            catch
            {
                return false;
            }
        }

        private void LoadThemePreference()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    var value = key?.GetValue(THEME_SETTING_KEY);
                    if (value is string stringValue && bool.TryParse(stringValue, out bool isDark))
                    {
                        _isDarkTheme = isDark;
                        return;
                    }
                }
            }
            catch
            {
                // If we can't read from registry, use default
            }

            // Default to light theme
            _isDarkTheme = false;
        }

        /// <summary>
        /// NON-BLOCKING theme preference saving - prevents UI freezing during registry operations
        /// </summary>
        private async Task SaveThemePreferenceAsync(bool isDarkTheme)
        {
            await Task.Run(() =>
            {
                try
                {
                    using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                    {
                        key?.SetValue(THEME_SETTING_KEY, isDarkTheme.ToString());
                    }
                }
                catch
                {
                    // If we can't write to registry, continue without saving
                    System.Diagnostics.Debug.WriteLine("Could not save theme preference to registry");
                }
            });
        }

        /// <summary>
        /// SYNCHRONOUS theme preference saving for backward compatibility
        /// </summary>
        private void SaveThemePreference(bool isDarkTheme)
        {
            Task.Run(async () => await SaveThemePreferenceAsync(isDarkTheme));
        }

        /// <summary>
        /// Load color scheme preference from registry
        /// </summary>
        private void LoadColorSchemePreference()
        {
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(REGISTRY_KEY))
                {
                    var value = key?.GetValue(COLOR_SCHEME_SETTING_KEY);
                    if (value is string stringValue && Enum.TryParse<ColorScheme>(stringValue, out ColorScheme colorScheme))
                    {
                        _colorScheme = colorScheme;
                        return;
                    }
                }
            }
            catch
            {
                // If we can't read from registry, use default
            }

            // Default to material blue color scheme
            _colorScheme = ColorScheme.MaterialBlue;
        }

        /// <summary>
        /// Save color scheme preference to registry
        /// </summary>
        private void SaveColorSchemePreference(ColorScheme colorScheme)
        {
            Task.Run(() =>
            {
                try
                {
                    using (var key = Registry.CurrentUser.CreateSubKey(REGISTRY_KEY))
                    {
                        key?.SetValue(COLOR_SCHEME_SETTING_KEY, colorScheme.ToString());
                    }
                }
                catch
                {
                    // If we can't write to registry, continue without saving
                    System.Diagnostics.Debug.WriteLine("Could not save color scheme preference to registry");
                }
            });
        }
    }
}
