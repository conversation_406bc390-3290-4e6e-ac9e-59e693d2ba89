﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;
using Metabolomics.MsLima.Bean;

namespace Metabolomics.MsLima.Model
{
    public class TabMassSpectraViewIntConverter : ValueConverterBase<TabMassSpectraView, int>
    {
        public override int Convert(TabMassSpectraView value, Type targetType, object parameter, CultureInfo culture)
        {
            return (int)value;
        }

        public override TabMassSpectraView ConvertBack(int value, Type targetType, object parameter, CultureInfo culture)
        {
            return (TabMassSpectraView)value;
        }
    }

    public class TabMassSpectraTableIntConverter : ValueConverterBase<TabMassSpectrumTable, int>
    {
        public override int Convert(TabMassSpectrumTable value, Type targetType, object parameter, CultureInfo culture)
        {
            return (int)value;
        }

        public override TabMassSpectrumTable ConvertBack(int value, Type targetType, object parameter, CultureInfo culture)
        {
            return (TabMassSpectrumTable)value;
        }
    }

    public class EnumToCllectionConverter : ValueConverterBase<CompoundGroupingKey, IEnumerable<string>>
    {
        public override IEnumerable<string> Convert(CompoundGroupingKey value, Type targetType, object parameter, CultureInfo culture)
        {
            return Enum.GetNames(typeof(CompoundGroupingKey)).ToList();
        }

        public override CompoundGroupingKey ConvertBack(IEnumerable<string> value, Type targetType, object parameter, CultureInfo culture)
        {
            return new CompoundGroupingKey();
            /*
            CompoundGroupingKey[] values = new CompoundGroupingKey[value.Count()];
            string[] names = Enum.GetNames(typeof(CompoundGroupingKey));


            for (int x = 0; x < values.Length; x++)
            {
                //Parse specific string to enum
                values[x] = (CompoundGroupingKey)Enum.Parse(typeof(CompoundGroupingKey), names[x]);
            }
            */
        }
    }

    /// <summary>
    /// Value converter that replaces null or empty values with "/" for better visual consistency
    /// </summary>
    public class NullToSlashConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Handle null values
            if (value == null)
                return "/";

            // Handle string values
            if (value is string stringValue)
            {
                return string.IsNullOrEmpty(stringValue) ? "/" : stringValue;
            }

            // Handle numeric values that might be zero or default
            if (value is double doubleValue)
            {
                return doubleValue == 0.0 ? "/" : value;
            }

            if (value is float floatValue)
            {
                return floatValue == 0.0f ? "/" : value;
            }

            if (value is int intValue)
            {
                return intValue == 0 ? "/" : value;
            }

            // For all other types, return the original value or "/" if it's the default value
            if (value.Equals(Activator.CreateInstance(value.GetType())))
                return "/";

            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Convert "/" back to null for two-way binding scenarios
            if (value is string stringValue && stringValue == "/")
            {
                if (targetType == typeof(string))
                    return null;
                if (targetType == typeof(double) || targetType == typeof(double?))
                    return 0.0;
                if (targetType == typeof(float) || targetType == typeof(float?))
                    return 0.0f;
                if (targetType == typeof(int) || targetType == typeof(int?))
                    return 0;
            }

            return value;
        }
    }

}
